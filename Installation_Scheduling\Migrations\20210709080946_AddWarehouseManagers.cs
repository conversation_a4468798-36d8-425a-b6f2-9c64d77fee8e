﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddWarehouseManagers : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "WarehouseManagerId",
                schema: "Store",
                table: "Warehouse",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "WarehouseManager",
                schema: "Ops",
                columns: table => new
                {
                    WarehouseManagerId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    WarehouseId = table.Column<int>(type: "int", nullable: false),
                    ApplicationUserId = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WarehouseManager", x => x.WarehouseManagerId);
                    table.ForeignKey(
                        name: "FK_WarehouseManager_Warehouse",
                        column: x => x.WarehouseId,
                        principalSchema: "Store",
                        principalTable: "Warehouse",
                        principalColumn: "WarehouseID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WarehouseManager_WarehouseId",
                schema: "Ops",
                table: "WarehouseManager",
                column: "WarehouseId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WarehouseManager",
                schema: "Ops");

            migrationBuilder.DropColumn(
                name: "WarehouseManagerId",
                schema: "Store",
                table: "Warehouse");
        }
    }
}
