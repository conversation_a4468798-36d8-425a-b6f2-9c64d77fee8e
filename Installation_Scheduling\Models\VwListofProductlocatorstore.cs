﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VwListofProductlocatorstore
    {
        public Guid ContractId { get; set; }
        public int StoreId { get; set; }
        public string StoreDescription { get; set; }
        public string StoreNumber { get; set; }
        public string ChainName { get; set; }
        public string StoreName { get; set; }
        public string CityName { get; set; }
        public string WarehouseName { get; set; }
        public string InstallationTeamName { get; set; }
        public int InstallationTeamTypeId { get; set; }
        public Guid? Consoleid { get; set; }
        public DateTime? Dateprogrammed { get; set; }
        public string Programmedby { get; set; }
        public bool Programmingconfirmed { get; set; }
    }
}
