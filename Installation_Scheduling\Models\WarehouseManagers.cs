﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class WarehouseManager
    {
        public int WarehouseManagerId { get; set; }
        public int WarehouseId { get; set; }
        public string ApplicationUserId { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        public virtual Warehouse Warehouse { get; set; }
    }
}
