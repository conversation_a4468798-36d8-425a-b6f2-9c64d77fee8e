﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Drawing.Imaging;
using System.Drawing;
using System.IO;
using MetadataExtractor;
using System.Text;
using System.Text.RegularExpressions;
using MetadataExtractor.Formats.Exif;

namespace PhoenixAPI.Controllers
{
    
    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    public class PhotoHanlerController : ControllerBase
    {
        private FileInfo f;
        private static Regex r = new Regex(":");
        [HttpGet]
        [Route("GetImages")]
        public async Task<object> ServeImage(string imageURL, string storeName = "")
        {
            //Byte[] b = System.IO.File.ReadAllBytes(@"\\192.168.0.16\Images\" + @imageURL);


            Image myImage = Image.FromFile(@"\\192.168.0.16\Images\" + @imageURL, true);

            f = new FileInfo(@"\\192.168.0.16\Images\" + @imageURL);
            //myImage = Image.FromFile(@"\\192.168.0.16\Images\" + @imageURL, true);
            //return myImage;
            Byte[] b = ImageToByteArray(myImage, 100L, storeName);// You can use your own method over here.         
            return File(b, "image/jpeg");
        }


        [HttpGet]
        [Route("GetImagesLQ")]
        public async Task<object> GetImagesLQ(string imageURL, string storeName = "")
        {
            //Byte[] b = System.IO.File.ReadAllBytes(@"\\192.168.0.16\Images\" + @imageURL);   // You can use your own method over here.     
            Image myImage = Image.FromFile(@"\\192.168.0.16\Images\" + @imageURL, true);

             f = new FileInfo(@"\\192.168.0.16\Images\" + @imageURL);
            //myImage = Image.FromFile(@"\\192.168.0.16\Images\" + @imageURL, true);
            //return myImage;
            Byte[] b = ImageToByteArray(myImage,25L, storeName);




            //if(propItem.Value == "")
            //{
            //    ChangeDateTaken((@"\\192.168.0.16\Images\" + @imageURL);
            //}
            var directories = ImageMetadataReader.ReadMetadata(
             new MemoryStream(b));

            // Read all metadata from the image
            //var directories = ImageMetadataReader.ReadMetadata(stream);

            // Find the so-called Exif "SubIFD" (which may be null)
            var subIfdDirectory = directories.OfType<ExifSubIfdDirectory>().FirstOrDefault();

            // Read the DateTime tag value
            var dateTime = subIfdDirectory?.GetDateTime(ExifDirectoryBase.TagDateTimeOriginal);




            return File(b, "image/jpeg");
        }

        public void ChangeDateTaken(string path)
        {
            Image theImage = new Bitmap(path);
            PropertyItem[] propItems = theImage.PropertyItems;
            Encoding _Encoding = Encoding.UTF8;
            var DataTakenProperty1 = propItems.Where(a => a.Id.ToString("x") == "9004").FirstOrDefault();
            var DataTakenProperty2 = propItems.Where(a => a.Id.ToString("x") == "9003").FirstOrDefault();

           
            DataTakenProperty1.Value = _Encoding.GetBytes(System.DateTime.Now.ToString("yyyy:MM:dd HH:mm:ss") + '\0');
            DataTakenProperty2.Value = _Encoding.GetBytes(System.DateTime.Now.ToString("yyyy:MM:dd HH:mm:ss") + '\0');
            theImage.SetPropertyItem(DataTakenProperty1);
            theImage.SetPropertyItem(DataTakenProperty2);
            string new_path = System.IO.Path.GetDirectoryName(path) + "\\_" + System.IO.Path.GetFileName(path);
            theImage.Save(new_path);
            theImage.Dispose();
        }

        private static ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            // Get image codecs for all image formats 
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();

            // Find the correct image codec 
            for (int i = 0; i < codecs.Length; i++)
                if (codecs[i].MimeType == mimeType)
                    return codecs[i];

            return null;
        }

        public byte[] ImageToByteArray(System.Drawing.Image imageIn, long quality = 100L,string storename = "")
        {
            EncoderParameter qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, quality);
           
            EncoderParameters encoderParams = new EncoderParameters(1);
            encoderParams.Param[0] = qualityParam;
            ImageFormat format = imageIn.RawFormat;
            ImageCodecInfo codec = ImageCodecInfo.GetImageDecoders().First(c => c.FormatID == format.Guid);
            string mimeType = codec.MimeType;

            using (var ms = new MemoryStream())
            {
                string firstText = f.LastWriteTime.ToShortDateString() + " " + f.LastWriteTime.ToShortTimeString();
                //string secondText = "World";
                if(storename != "")
                {
                    firstText = firstText;
                }
                PointF firstLocation = new PointF(10f, 10f);
                PointF secondLocation = new PointF(10f, 140f);
                using (Graphics graphics = Graphics.FromImage(imageIn))
                {
                    using (Font arialFont = new Font("Arial", 100))
                    {
                        graphics.DrawString(firstText, arialFont, Brushes.Red, firstLocation);
                        graphics.DrawString(storename, arialFont, Brushes.Red, secondLocation);
                    }
                }
                imageIn.Save(ms, codec, encoderParams);
                return ms.ToArray();
            }
        }
    }
}
