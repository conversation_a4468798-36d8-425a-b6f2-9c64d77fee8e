﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Models.IncomingMedia
{
    public class regionIncomingMediaResult
    {
        public string InstallationDate { get; set; }
        public string ContractNumber { get; set; }
        public string Region { get; set; }
        public string MediaType { get; set; }
        public string Campaign { get; set; }
        public System.Guid ContractId { get; set; }
        public int TotalAdsInstallationQty { get; set; }
    }
}
