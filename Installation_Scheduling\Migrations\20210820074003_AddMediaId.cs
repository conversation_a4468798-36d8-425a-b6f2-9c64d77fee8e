﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddMediaId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MediaId",
                schema: "Ops",
                table: "Questions",
                type: "int",
                nullable: true,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_Questions_MediaId",
                schema: "Ops",
                table: "Questions",
                column: "MediaId");

            migrationBuilder.AddForeignKey(
                name: "FK_Questions_Media_MediaId",
                schema: "Ops",
                table: "Questions",
                column: "MediaId",
                principalSchema: "Media",
                principalTable: "Media",
                principalColumn: "MediaID",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Questions_Media_MediaId",
                schema: "Ops",
                table: "Questions");

            migrationBuilder.DropIndex(
                name: "IX_Questions_MediaId",
                schema: "Ops",
                table: "Questions");

            migrationBuilder.DropColumn(
                name: "MediaId",
                schema: "Ops",
                table: "Questions");
        }
    }
}
