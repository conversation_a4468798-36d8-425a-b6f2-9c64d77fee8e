﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Models.IncomingMedia
{
    public class IncomingMediaResult
    {
        public string CreationDate { get; set; }
        public string ContractNumber { get; set; }
        public string Campaign { get; set; }
        public string Chain { get; set; }
        public string MediaType { get; set; }
        public int MediaRequiredQty { get; set; }
        public int StoreListQty { get; set; }
        public string ContractId { get; set; }
        public string Region { get; set; }
    }
}
