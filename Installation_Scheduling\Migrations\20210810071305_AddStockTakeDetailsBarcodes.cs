﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddStockTakeDetailsBarcodes : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            

            migrationBuilder.CreateTable(
                name: "InventoryStockTakeDetailsBarcodes",
                schema: "Ops",
                columns: table => new
                {
                    InventoryStockTakeDetailsBarcodesId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    InventoryStockTakeDetailId = table.Column<int>(type: "int", nullable: false),
                    WarehouseId = table.Column<int>(type: "int", nullable: true),
                    InstallationTeamId = table.Column<int>(type: "int", nullable: true),
                    userName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    isWrongCapex = table.Column<bool>(type: "bit", nullable: false, defaultValueSql: "((0))"),
                    isNonExisting = table.Column<bool>(type: "bit", nullable: false, defaultValueSql: "((0))"),
                    MasterItemId = table.Column<int>(type: "int", nullable: true),
                    MasterItemInventoryId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryStockTakeDetailsBarcodes", x => x.InventoryStockTakeDetailsBarcodesId);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetailsBarcodes_InstallationTeam_InstallationTeamId",
                        column: x => x.InstallationTeamId,
                        principalSchema: "Ops",
                        principalTable: "InstallationTeam",
                        principalColumn: "InstallationTeamID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetailsBarcodes_InventoryStockTakeDetail",
                        column: x => x.InventoryStockTakeDetailId,
                        principalSchema: "Ops",
                        principalTable: "InventoryStockTakeDetails",
                        principalColumn: "InventoryStockTakeDetailId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetailsBarcodes_MasterItem_MasterItemId",
                        column: x => x.MasterItemId,
                        principalSchema: "Ops",
                        principalTable: "MasterItem",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetailsBarcodes_MasterItemInventoryItem_MasterItemInventoryId",
                        column: x => x.MasterItemInventoryId,
                        principalSchema: "Ops",
                        principalTable: "MasterItemInventoryItem",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetailsBarcodes_Warehouse_WarehouseId",
                        column: x => x.WarehouseId,
                        principalSchema: "Store",
                        principalTable: "Warehouse",
                        principalColumn: "WarehouseID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetailsBarcodes_InstallationTeamId",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes",
                column: "InstallationTeamId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetailsBarcodes_InventoryStockTakeDetailId",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes",
                column: "InventoryStockTakeDetailId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetailsBarcodes_MasterItemId",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes",
                column: "MasterItemId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetailsBarcodes_MasterItemInventoryId",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes",
                column: "MasterItemInventoryId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetailsBarcodes_WarehouseId",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes",
                column: "WarehouseId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InventoryStockTakeDetailsBarcodes",
                schema: "Ops");

           
        }
    }
}
