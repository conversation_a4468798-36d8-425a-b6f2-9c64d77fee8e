﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddIsOptionalToMediaCapexOpex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "isOptional",
                schema: "Media",
                table: "MediaCapexOpex",
                type: "bit",
                nullable: false,
                defaultValueSql: "((0))");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "isOptional",
                schema: "Media",
                table: "MediaCapexOpex");
        }
    }
}
