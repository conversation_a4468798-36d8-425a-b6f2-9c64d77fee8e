﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddMediaTypeToMediaMonitorsTwo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropForeignKey(
            //    name: "FK_Burst_Contract",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.DropIndex(
            //    name: "INDX_Sales_Burst_ContractID",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.RenameColumn(
            //    name: "ContractID",
            //    schema: "Sales",
            //    table: "Burst",
            //    newName: "ContractId");

            //migrationBuilder.AddColumn<int>(
            //    name: "RegionId",
            //    schema: "Store",
            //    table: "Warehouse",
            //    type: "int",
            //    nullable: true);

            //migrationBuilder.AddColumn<int>(
            //    name: "ShelfId",
            //    schema: "Ops",
            //    table: "InventoryStockTakeDetailsBarcodes",
            //    type: "int",
            //    nullable: true);

            //migrationBuilder.AddColumn<int>(
            //    name: "RegionId",
            //    schema: "Ops",
            //    table: "InstallationTeam",
            //    type: "int",
            //    nullable: true);

            //migrationBuilder.AddColumn<Guid>(
            //    name: "InstallationScheduleArchivedID",
            //    schema: "Ops",
            //    table: "InstallationScheduleQuestionsAndAnswers",
            //    type: "uniqueidentifier",
            //    nullable: true);

            //migrationBuilder.AddColumn<Guid>(
            //    name: "ContractId",
            //    schema: "Ops",
            //    table: "InstallationInstruction",
            //    type: "uniqueidentifier",
            //    nullable: true);

            //migrationBuilder.AlterColumn<Guid>(
            //    name: "ContractId",
            //    schema: "Sales",
            //    table: "Burst",
            //    type: "uniqueidentifier",
            //    nullable: true,
            //    oldClrType: typeof(Guid),
            //    oldType: "uniqueidentifier");

            //migrationBuilder.AddColumn<Guid>(
            //    name: "ContractID",
            //    schema: "Sales",
            //    table: "Burst",
            //    type: "uniqueidentifier",
            //    nullable: false,
            //    defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            //migrationBuilder.CreateTable(
            //    name: "IncomingMediaPerRegion",
            //    schema: "Ops",
            //    columns: table => new
            //    {
            //        Id = table.Column<int>(type: "int", nullable: false)
            //            .Annotation("SqlServer:Identity", "1, 1"),
            //        ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
            //        Campaign = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        ContractNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        CreationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
            //        MediaType = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        RegionId = table.Column<int>(type: "int", nullable: true),
            //        RegionName = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        DeliveryNote = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        BalanceQty = table.Column<int>(type: "int", nullable: true),
            //        ReceivedInitiallyQty = table.Column<int>(type: "int", nullable: true)
            //    },
            //    constraints: table =>
            //    {
            //        table.PrimaryKey("PK_Ops.IncomingMediaPerRegion", x => x.Id);
            //    });

            //migrationBuilder.CreateTable(
            //    name: "InstallationScheduleArchived",
            //    schema: "Ops",
            //    columns: table => new
            //    {
            //        InstallationScheduleArchivedID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
            //        InstallationScheduleCurrentID = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
            //        Group = table.Column<string>(type: "varchar(6)", unicode: false, maxLength: 6, nullable: false),
            //        DayofCommencementDate = table.Column<string>(name: "Day of Commencement Date", type: "varchar(8000)", unicode: false, maxLength: 8000, nullable: true),
            //        DayofLastmodified = table.Column<int>(name: "Day of Last modified", type: "int", nullable: true),
            //        DayofTerminationdate = table.Column<string>(name: "Day of Termination date", type: "varchar(8000)", unicode: false, maxLength: 8000, nullable: true),
            //        QuarterofCommencementdate = table.Column<int>(name: "Quarter of Commencement date", type: "int", nullable: true),
            //        MonthofCommencementdate = table.Column<int>(name: "Month of Commencement date", type: "int", nullable: true),
            //        WeekofCommencementdate = table.Column<int>(name: "Week of Commencement date", type: "int", nullable: true),
            //        QuarterofLastmodified = table.Column<int>(name: "Quarter of Last modified", type: "int", nullable: true),
            //        MonthofLastmodified = table.Column<int>(name: "Month of Last modified", type: "int", nullable: true),
            //        WeekofLastmodified = table.Column<int>(name: "Week of Last modified", type: "int", nullable: true),
            //        QuarterofTerminationdate = table.Column<int>(name: "Quarter of Termination date", type: "int", nullable: true),
            //        MonthofTerminationdate = table.Column<int>(name: "Month of Termination date", type: "int", nullable: true),
            //        WeekofTerminationdate = table.Column<int>(name: "Week of Termination date", type: "int", nullable: true),
            //        YearofCommencementdate = table.Column<int>(name: "Year of Commencement date", type: "int", nullable: true),
            //        YearofLastmodified = table.Column<int>(name: "Year of Last modified", type: "int", nullable: true),
            //        YearofTerminationdate = table.Column<int>(name: "Year of Termination date", type: "int", nullable: true),
            //        JobNumber = table.Column<string>(name: "Job Number", type: "nvarchar(8)", maxLength: 8, nullable: false),
            //        SpecialInstructions = table.Column<string>(name: "Special Instructions", type: "varchar(8000)", unicode: false, maxLength: 8000, nullable: true),
            //        Cycle = table.Column<int>(type: "int", nullable: true),
            //        Numberofweeks = table.Column<int>(name: "Number of weeks", type: "int", nullable: true),
            //        CommencementDate = table.Column<int>(name: "Commencement Date", type: "int", nullable: true),
            //        TerminationDate = table.Column<int>(name: "Termination Date", type: "int", nullable: true),
            //        Store = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
            //        Region = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
            //        Product = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
            //        MediaType = table.Column<string>(name: "Media Type", type: "nvarchar(213)", maxLength: 213, nullable: false),
            //        Client = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: false),
            //        CategoryName = table.Column<string>(name: "Category Name", type: "nvarchar(200)", maxLength: 200, nullable: false),
            //        LastModified = table.Column<string>(name: "Last Modified", type: "nvarchar(19)", maxLength: 19, nullable: true),
            //        OpenedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
            //        FinishedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
            //        Chain = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: false),
            //        Quantity = table.Column<int>(type: "int", nullable: true),
            //        Lastmodifiedby = table.Column<int>(name: "Last modified by", type: "int", nullable: true),
            //        Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
            //        StoreandChain = table.Column<int>(name: "Store and Chain", type: "int", nullable: true),
            //        CreatedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
            //        Section = table.Column<int>(type: "int", nullable: true),
            //        ForDate = table.Column<string>(type: "nchar(12)", fixedLength: true, maxLength: 12, nullable: true),
            //        sto_id = table.Column<int>(type: "int", nullable: false),
            //        teamlist = table.Column<int>(type: "int", nullable: true),
            //        QtyToInstall = table.Column<int>(type: "int", nullable: false),
            //        InstallationDay = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        IRCodeComment = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        GeneratedScheduleDate = table.Column<DateTime>(type: "datetime", nullable: true),
            //        CampaignSpecialInstructionsRead = table.Column<bool>(type: "bit", nullable: false),
            //        CampaignPictureTaken = table.Column<bool>(type: "bit", nullable: false),
            //        CampaignIRCodeSelected = table.Column<bool>(type: "bit", nullable: false),
            //        CampaignFinished = table.Column<bool>(type: "bit", nullable: false),
            //        CampaignPicturePath = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        IRCodeID = table.Column<int>(type: "int", nullable: false),
            //        PreviousIRCodeId = table.Column<int>(type: "int", nullable: true),
            //        PreviousIRCodeComment = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        MediaId = table.Column<int>(type: "int", nullable: false),
            //        Installationteamid = table.Column<int>(type: "int", nullable: false),
            //        IsCurrent = table.Column<bool>(type: "bit", nullable: false),
            //        AllowPictureAfterwards = table.Column<bool>(type: "bit", nullable: false, defaultValueSql: "((0))"),
            //        imageURL = table.Column<string>(type: "nvarchar(max)", nullable: true, defaultValueSql: "('')")
            //    },
            //    constraints: table =>
            //    {
            //        table.PrimaryKey("PK_InstallationScheduleArchived", x => x.InstallationScheduleArchivedID);
            //        table.ForeignKey(
            //            name: "FK_InstallationScheduleArchived_InstallationTeam_Installationteamid",
            //            column: x => x.Installationteamid,
            //            principalSchema: "Ops",
            //            principalTable: "InstallationTeam",
            //            principalColumn: "InstallationTeamID",
            //            onDelete: ReferentialAction.Cascade);
            //        table.ForeignKey(
            //            name: "FK_InstallationScheduleArchived_IRCodes_IRCodeID",
            //            column: x => x.IRCodeID,
            //            principalSchema: "Ops",
            //            principalTable: "IRCodes",
            //            principalColumn: "IRCodeID",
            //            onDelete: ReferentialAction.Cascade);
            //        table.ForeignKey(
            //            name: "FK_InstallationScheduleArchived_IRCodes_PreviousIRCodeId",
            //            column: x => x.PreviousIRCodeId,
            //            principalSchema: "Ops",
            //            principalTable: "IRCodes",
            //            principalColumn: "IRCodeID",
            //            onDelete: ReferentialAction.Restrict);
            //        table.ForeignKey(
            //            name: "FK_InstallationScheduleArchived_Media_MediaId",
            //            column: x => x.MediaId,
            //            principalSchema: "Media",
            //            principalTable: "Media",
            //            principalColumn: "MediaID",
            //            onDelete: ReferentialAction.Cascade);
            //    });

            //migrationBuilder.CreateTable(
            //    name: "MediaMonitor",
            //    schema: "Ops",
            //    columns: table => new
            //    {
            //        Id = table.Column<int>(type: "int", nullable: false)
            //            .Annotation("SqlServer:Identity", "1, 1"),
            //        ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
            //        ImagePath = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        BalanceQty = table.Column<int>(type: "int", nullable: true),
            //        ReceivedBackQty = table.Column<int>(type: "int", nullable: true),
            //        ReceivedInitiallyQty = table.Column<int>(type: "int", nullable: true),
            //        LostQty = table.Column<int>(type: "int", nullable: true),
            //        RegionName = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        CampaignNote = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        DeliveryNote = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        mediaType = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        CreationDate = table.Column<DateTime>(type: "datetime2", nullable: true)
            //    },
            //    constraints: table =>
            //    {
            //        table.PrimaryKey("PK_Ops.MediaMonitor", x => x.Id);
            //    });

            //migrationBuilder.CreateTable(
            //    name: "MediaMonitorRegions",
            //    schema: "Ops",
            //    columns: table => new
            //    {
            //        RegionId = table.Column<int>(type: "int", nullable: false)
            //            .Annotation("SqlServer:Identity", "1, 1"),
            //        RegionName = table.Column<string>(type: "nvarchar(max)", nullable: true)
            //    },
            //    constraints: table =>
            //    {
            //        table.PrimaryKey("PK_Ops.MediaMonitorRegions", x => x.RegionId);
            //    });

            //migrationBuilder.CreateTable(
            //    name: "Region",
            //    schema: "Ops",
            //    columns: table => new
            //    {
            //        RegionID = table.Column<int>(type: "int", nullable: false)
            //            .Annotation("SqlServer:Identity", "1, 1"),
            //        RegionCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        RegionName = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        Dormant = table.Column<bool>(type: "bit", nullable: false),
            //        CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true, defaultValueSql: "(suser_sname())"),
            //        CreationDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "(getdate())")
            //    },
            //    constraints: table =>
            //    {
            //        table.PrimaryKey("PK_Ops.Region", x => x.RegionID);
            //    });

            //migrationBuilder.CreateIndex(
            //    name: "IX_Warehouse_RegionId",
            //    schema: "Store",
            //    table: "Warehouse",
            //    column: "RegionId");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InventoryStockTakeDetailsBarcodes_ShelfId",
            //    schema: "Ops",
            //    table: "InventoryStockTakeDetailsBarcodes",
            //    column: "ShelfId");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationTeam_RegionId",
            //    schema: "Ops",
            //    table: "InstallationTeam",
            //    column: "RegionId");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationScheduleQuestionsAndAnswers_InstallationScheduleArchivedID",
            //    schema: "Ops",
            //    table: "InstallationScheduleQuestionsAndAnswers",
            //    column: "InstallationScheduleArchivedID");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationInstruction_ContractId",
            //    schema: "Ops",
            //    table: "InstallationInstruction",
            //    column: "ContractId",
            //    unique: true,
            //    filter: "[ContractId] IS NOT NULL");

            //migrationBuilder.CreateIndex(
            //    name: "INDX_Sales_Burst_ContractID",
            //    schema: "Sales",
            //    table: "Burst",
            //    column: "ContractID")
            //    .Annotation("SqlServer:FillFactor", 75);

            //migrationBuilder.CreateIndex(
            //    name: "IX_Burst_ContractId",
            //    schema: "Sales",
            //    table: "Burst",
            //    column: "ContractId",
            //    unique: true,
            //    filter: "[ContractId] IS NOT NULL");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationScheduleArchived_Installationteamid",
            //    schema: "Ops",
            //    table: "InstallationScheduleArchived",
            //    column: "Installationteamid");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationScheduleArchived_IRCodeID",
            //    schema: "Ops",
            //    table: "InstallationScheduleArchived",
            //    column: "IRCodeID");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationScheduleArchived_MediaId",
            //    schema: "Ops",
            //    table: "InstallationScheduleArchived",
            //    column: "MediaId");

            //migrationBuilder.CreateIndex(
            //    name: "IX_InstallationScheduleArchived_PreviousIRCodeId",
            //    schema: "Ops",
            //    table: "InstallationScheduleArchived",
            //    column: "PreviousIRCodeId");

            //migrationBuilder.AddForeignKey(
            //    name: "FK_Burst_Contract_ContractId",
            //    schema: "Sales",
            //    table: "Burst",
            //    column: "ContractId",
            //    principalSchema: "Sales",
            //    principalTable: "Contract",
            //    principalColumn: "ContractID",
            //    onDelete: ReferentialAction.Restrict);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_Burst_Contract_ContractID",
            //    schema: "Sales",
            //    table: "Burst",
            //    column: "ContractID",
            //    principalSchema: "Sales",
            //    principalTable: "Contract",
            //    principalColumn: "ContractID",
            //    onDelete: ReferentialAction.Cascade);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_ContractApproved_Contract_ContractID",
            //    schema: "Sales",
            //    table: "ContractApproved",
            //    column: "ContractID",
            //    principalSchema: "Sales",
            //    principalTable: "Contract",
            //    principalColumn: "ContractID",
            //    onDelete: ReferentialAction.Cascade);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_InstallationInstruction_Contract_ContractId",
            //    schema: "Ops",
            //    table: "InstallationInstruction",
            //    column: "ContractId",
            //    principalSchema: "Sales",
            //    principalTable: "Contract",
            //    principalColumn: "ContractID",
            //    onDelete: ReferentialAction.Restrict);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_InstallationScheduleQuestionsAndAnswers_InstallationScheduleArchived_InstallationScheduleArchivedID",
            //    schema: "Ops",
            //    table: "InstallationScheduleQuestionsAndAnswers",
            //    column: "InstallationScheduleArchivedID",
            //    principalSchema: "Ops",
            //    principalTable: "InstallationScheduleArchived",
            //    principalColumn: "InstallationScheduleArchivedID",
            //    onDelete: ReferentialAction.Restrict);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_InstallationTeam_Region_RegionId",
            //    schema: "Ops",
            //    table: "InstallationTeam",
            //    column: "RegionId",
            //    principalSchema: "Ops",
            //    principalTable: "Region",
            //    principalColumn: "RegionID",
            //    onDelete: ReferentialAction.Restrict);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_InventoryStockTakeDetailsBarcodes_Shelves_ShelfId",
            //    schema: "Ops",
            //    table: "InventoryStockTakeDetailsBarcodes",
            //    column: "ShelfId",
            //    principalSchema: "Ops",
            //    principalTable: "Shelves",
            //    principalColumn: "ShelfId",
            //    onDelete: ReferentialAction.Restrict);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_Warehouse_Region_RegionId",
            //    schema: "Store",
            //    table: "Warehouse",
            //    column: "RegionId",
            //    principalSchema: "Ops",
            //    principalTable: "Region",
            //    principalColumn: "RegionID",
            //    onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropForeignKey(
            //    name: "FK_Burst_Contract_ContractId",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_Burst_Contract_ContractID",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_ContractApproved_Contract_ContractID",
            //    schema: "Sales",
            //    table: "ContractApproved");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_InstallationInstruction_Contract_ContractId",
            //    schema: "Ops",
            //    table: "InstallationInstruction");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_InstallationScheduleQuestionsAndAnswers_InstallationScheduleArchived_InstallationScheduleArchivedID",
            //    schema: "Ops",
            //    table: "InstallationScheduleQuestionsAndAnswers");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_InstallationTeam_Region_RegionId",
            //    schema: "Ops",
            //    table: "InstallationTeam");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_InventoryStockTakeDetailsBarcodes_Shelves_ShelfId",
            //    schema: "Ops",
            //    table: "InventoryStockTakeDetailsBarcodes");

            //migrationBuilder.DropForeignKey(
            //    name: "FK_Warehouse_Region_RegionId",
            //    schema: "Store",
            //    table: "Warehouse");

            //migrationBuilder.DropTable(
            //    name: "IncomingMediaPerRegion",
            //    schema: "Ops");

            //migrationBuilder.DropTable(
            //    name: "InstallationScheduleArchived",
            //    schema: "Ops");

            //migrationBuilder.DropTable(
            //    name: "MediaMonitor",
            //    schema: "Ops");

            //migrationBuilder.DropTable(
            //    name: "MediaMonitorRegions",
            //    schema: "Ops");

            //migrationBuilder.DropTable(
            //    name: "Region",
            //    schema: "Ops");

            //migrationBuilder.DropIndex(
            //    name: "IX_Warehouse_RegionId",
            //    schema: "Store",
            //    table: "Warehouse");

            //migrationBuilder.DropIndex(
            //    name: "IX_InventoryStockTakeDetailsBarcodes_ShelfId",
            //    schema: "Ops",
            //    table: "InventoryStockTakeDetailsBarcodes");

            //migrationBuilder.DropIndex(
            //    name: "IX_InstallationTeam_RegionId",
            //    schema: "Ops",
            //    table: "InstallationTeam");

            //migrationBuilder.DropIndex(
            //    name: "IX_InstallationScheduleQuestionsAndAnswers_InstallationScheduleArchivedID",
            //    schema: "Ops",
            //    table: "InstallationScheduleQuestionsAndAnswers");

            //migrationBuilder.DropIndex(
            //    name: "IX_InstallationInstruction_ContractId",
            //    schema: "Ops",
            //    table: "InstallationInstruction");

            //migrationBuilder.DropIndex(
            //    name: "INDX_Sales_Burst_ContractID",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.DropIndex(
            //    name: "IX_Burst_ContractId",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.DropColumn(
            //    name: "RegionId",
            //    schema: "Store",
            //    table: "Warehouse");

            //migrationBuilder.DropColumn(
            //    name: "ShelfId",
            //    schema: "Ops",
            //    table: "InventoryStockTakeDetailsBarcodes");

            //migrationBuilder.DropColumn(
            //    name: "RegionId",
            //    schema: "Ops",
            //    table: "InstallationTeam");

            //migrationBuilder.DropColumn(
            //    name: "InstallationScheduleArchivedID",
            //    schema: "Ops",
            //    table: "InstallationScheduleQuestionsAndAnswers");

            //migrationBuilder.DropColumn(
            //    name: "ContractId",
            //    schema: "Ops",
            //    table: "InstallationInstruction");

            //migrationBuilder.DropColumn(
            //    name: "ContractID",
            //    schema: "Sales",
            //    table: "Burst");

            //migrationBuilder.RenameColumn(
            //    name: "ContractId",
            //    schema: "Sales",
            //    table: "Burst",
            //    newName: "ContractID");

            //migrationBuilder.AlterColumn<Guid>(
            //    name: "ContractID",
            //    schema: "Sales",
            //    table: "Burst",
            //    type: "uniqueidentifier",
            //    nullable: false,
            //    defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            //    oldClrType: typeof(Guid),
            //    oldType: "uniqueidentifier",
            //    oldNullable: true);

            //migrationBuilder.CreateIndex(
            //    name: "INDX_Sales_Burst_ContractID",
            //    schema: "Sales",
            //    table: "Burst",
            //    column: "ContractID")
            //    .Annotation("SqlServer:FillFactor", 75);

            //migrationBuilder.AddForeignKey(
            //    name: "FK_Burst_Contract",
            //    schema: "Sales",
            //    table: "Burst",
            //    column: "ContractID",
            //    principalSchema: "Sales",
            //    principalTable: "Contract",
            //    principalColumn: "ContractID",
            //    onDelete: ReferentialAction.Cascade);
        }
    }
}
