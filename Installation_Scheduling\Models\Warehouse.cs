﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class Warehouse
    {
        public Warehouse()
        {
            InventoryItemMovementFromWarehouses = new HashSet<InventoryItemMovement>();
            InventoryItemMovementToWarehouses = new HashSet<InventoryItemMovement>();
            MasterItemInventoryItems = new HashSet<MasterItemInventoryItem>();
            Shelves = new HashSet<Shelf>();
            Stores = new HashSet<Store>();
        }

        public int WarehouseId { get; set; }
        public string WarehouseName { get; set; }
        public string PhysicalAddressLine1 { get; set; }
        public string PhysicalAddressLine2 { get; set; }
        public int? PhysicalAddressCityId { get; set; }
        public string ContactPerson { get; set; }
        public string ContactNumber { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreationDate { get; set; }
        public int? LocationTypeId { get; set; }
        public string WarehouseCode { get; set; }
        [Column("ApplicationUserId")]
        public string ApplicationUserId { get; set; }
        public int? RegionId { get; set; }
        public string WarehouseManagerId { get; set; }
        [ForeignKey(nameof(LocationTypeId))]
        public virtual LocationType LocationType { get; set; }
        public virtual ICollection<InventoryItemMovement> InventoryItemMovementFromWarehouses { get; set; }
        public virtual ICollection<InventoryItemMovement> InventoryItemMovementToWarehouses { get; set; }

        public virtual ICollection<InventoryItemTransactions> InventoryItemTransactionsFromWarehouses { get; set; }
        public virtual ICollection<InventoryItemTransactions> InventoryItemTransactionsToWarehouses { get; set; }
        public virtual ICollection<MasterItemInventoryItem> MasterItemInventoryItems { get; set; }
        public virtual ICollection<Shelf> Shelves { get; set; }
        public virtual ICollection<Store> Stores { get; set; }
        [ForeignKey(nameof(WarehouseManagerId))]
        public virtual ICollection<WarehouseManager> WarehouseManagers { get; set; }

        [ForeignKey(nameof(RegionId))]
        public virtual OpsRegion Region { get; set; }
    }
}
