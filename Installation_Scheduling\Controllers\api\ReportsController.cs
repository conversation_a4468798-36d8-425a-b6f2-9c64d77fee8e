﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PhoenixAPI.Models;
using PhoenixAPI.Entities;
using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Interfaces;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
using System.Data;
using Newtonsoft.Json;

namespace PhoenixAPI.Controllers
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "Admin")]
    public class ReportsController : ControllerBase
    {
        private readonly NovaDBContext _context = new NovaDBContext();
        //private static string pathForImages = @"\\192.168.0.16\Images";
        private string pathForImages = @"D:\Inetpub\wwwRoot\wwwPcaAdmin\kjoiweDeoijvwflvpaVsxW9tTvclidQpDsnikrc143ion\Images";

        //ok, here we will handle all store logic
        [Route("GetAuditCounts")]
        [HttpGet]
        public IEnumerable<CountReport> GetCountReports()
        {
            //Querying with LINQ to Entities 

            var result = from Contract in _context.Contract
                         join dCalender in _context.DateCalendar on Contract.CreationDate.Date equals dCalender.CalendarDate.Date into ContractWithCalendarDate
                         from m in ContractWithCalendarDate.DefaultIfEmpty()
                         where m.FirstDayInWeek.Date.Year == 2019
                         select new
                         {
                             ContractId = Contract.ContractId,
                             Createdby = Contract.CreatedBy,
                             Firstweek = m.FirstDayInWeek.Date

                         };



            List<CountReport> lstCountReport = result
                .GroupBy(x => new { x.Createdby, x.Firstweek })
                   .Select(g => new CountReport { Creator = g.Key.Createdby, DateRange = g.Key.Firstweek, Amount = g.Count() }).ToList();

            return lstCountReport;
        }



        [Route("GetContractCounts")]
        [HttpGet]
        public IEnumerable<InstallationDays> GetInstallationDays()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                return context.InstallationDays
                .ToList().OrderBy(x => x.InstallationDayId);
            }

        }



        public class CountReport
        {
            public string Creator;
            public int Amount;
            public DateTime DateRange;
        }


        //lets do the api's for the Strike Rate Report.
        [Route("StrikeRateCampaigsAndTeams")]
        [HttpGet]
        public IEnumerable<StrikeRateMasterDetails> StrikeRateCampaigsAndTeams()
        {
            List<StrikeRateMasterDetails> lstStrikeRateMasterDetails = new List<StrikeRateMasterDetails>();
            //Querying with LINQ to Entities 
            using (var db = new NovaDBContext())
            {
                var results = db.ImportedInstallationActions.ToList();
                lstStrikeRateMasterDetails = results.GroupBy(x => new { x.ContractNumber, x.Owner }).Select(y => new StrikeRateMasterDetails() { contractNumber = y.Key.ContractNumber, team = y.Key.Owner }).OrderBy(z => z.contractNumber).ToList();
            }
            return lstStrikeRateMasterDetails;
        }

        //lets do the api's for the Strike Rate Report.
        [Route("GenerateStrikeRateReport")]
        [HttpPost]
        public StrikeRateReport GenerateStrikeRateReport(StrikeRateMasterDetails strikeRateMasterDetails)
        {
            StrikeRateReport strikeRateReport = new StrikeRateReport();
            try
            {
                if (strikeRateMasterDetails.team.Length > 0 && strikeRateMasterDetails.contractNumber.Length > 0)
                {
                    using (var db = new NovaDBContext())
                    {

                        //lets do it differently here
                        var query = from ImportedInstallationActions in db.Set<ImportedInstallationActions>().Where(x => x.Owner.ToLower() == strikeRateMasterDetails.team && x.ContractNumber.ToLower() == strikeRateMasterDetails.contractNumber.ToLower() && x.ScheduleDate >= strikeRateMasterDetails.startDate && x.ScheduleDate <= strikeRateMasterDetails.endDate)
                                    join IRCodeS in db.Set<IRCodes>()
                                        on ImportedInstallationActions.IRCode equals IRCodeS.IRCodeName
                                    select new { ImportedInstallationActions, IRCodeS }
                                    ;

                        //now we have it joined
                        strikeRateReport.Name = query.Select(x => x.ImportedInstallationActions.Category).FirstOrDefault().ToString() + " - " + query.Select(x => x.ImportedInstallationActions.MyMobilityCategory).FirstOrDefault().ToString();
                        strikeRateReport.Route = strikeRateMasterDetails.team;
                        strikeRateReport.ContractNumber = strikeRateMasterDetails.contractNumber;
                        strikeRateReport.Type = "Team";
                        strikeRateReport.NoOfStoresSelected = query.Count();

                        var allWithAddNotUpIRCode = query.Where(x => (x.IRCodeS.IRCodeName.ToLower().Contains("anu") || (x.IRCodeS.IRCodeName.ToLower().Contains("add not up")))).ToList();
                        var allWithAddUpIrCode = query.Where(x => (!x.IRCodeS.IRCodeName.ToLower().Contains("anu") && (!x.IRCodeS.IRCodeName.ToLower().Contains("add not up")))).ToList();

                        //ok, let us now do calculations here
                        strikeRateReport.NoOfNonInstallations = allWithAddNotUpIRCode.Count();
                        strikeRateReport.NoOfAddsUp = strikeRateReport.NoOfStoresSelected - strikeRateReport.NoOfNonInstallations;

                        //ok, now we need to do it for the strike rate though.
                        float NoOfNonInstallationForStrikeRate = allWithAddNotUpIRCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        float NoOfInstallationsForStrikeRate = allWithAddUpIrCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        float allForStrikeRate = query.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        if (allForStrikeRate == 0)
                        {
                            allForStrikeRate = strikeRateReport.NoOfStoresSelected;
                        }
                        //lets calculate.
                        strikeRateReport.OverallCompliance = (decimal)(NoOfInstallationsForStrikeRate / allForStrikeRate) * 100;
                        //ok, we now have everything except total
                    }
                }
                else if (strikeRateMasterDetails.team.Length > 0)
                {
                    //we need to do it on the team
                    using (var db = new NovaDBContext())
                    {

                        //lets do it differently here
                        var query = from ImportedInstallationActions in db.Set<ImportedInstallationActions>().Where(x => x.Owner.ToLower() == strikeRateMasterDetails.team && x.ScheduleDate >= strikeRateMasterDetails.startDate && x.ScheduleDate <= strikeRateMasterDetails.endDate)
                                    join IRCodeS in db.Set<IRCodes>()
                                        on ImportedInstallationActions.IRCode equals IRCodeS.IRCodeName
                                    select new { ImportedInstallationActions, IRCodeS }
                                    ;

                        //now we have it joined
                        //strikeRateReport.Name = query.Select(x => x.ImportedInstallationActions.Category).FirstOrDefault().ToString() + " - " + query.Select(x => x.ImportedInstallationActions.MyMobilityCategory).FirstOrDefault().ToString();
                        strikeRateReport.Route = strikeRateMasterDetails.team;
                        strikeRateReport.Type = "Team";
                        strikeRateReport.NoOfStoresSelected = query.Count();
                        strikeRateReport.ContractNumber = strikeRateMasterDetails.contractNumber;

                        var allWithAddNotUpIRCode = query.Where(x => (x.IRCodeS.IRCodeName.ToLower().Contains("anu") || (x.IRCodeS.IRCodeName.ToLower().Contains("add not up")))).ToList();
                        var allWithAddUpIrCode = query.Where(x => (!x.IRCodeS.IRCodeName.ToLower().Contains("anu") && (!x.IRCodeS.IRCodeName.ToLower().Contains("add not up")))).ToList();

                        //ok, let us now do calculations here
                        strikeRateReport.NoOfNonInstallations = allWithAddNotUpIRCode.Count();
                        strikeRateReport.NoOfAddsUp = strikeRateReport.NoOfStoresSelected - strikeRateReport.NoOfNonInstallations;

                        //ok, now we need to do it for the strike rate though.
                        float NoOfNonInstallationForStrikeRate = allWithAddNotUpIRCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        float NoOfInstallationsForStrikeRate = allWithAddUpIrCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        float allForStrikeRate = query.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        if (allForStrikeRate == 0)
                        {
                            allForStrikeRate = strikeRateReport.NoOfStoresSelected;
                        }
                        //lets calculate.
                        strikeRateReport.OverallCompliance = (decimal)(NoOfInstallationsForStrikeRate / allForStrikeRate) * 100;
                        //ok, we now have everything except total
                    }
                }
                else if (strikeRateMasterDetails.contractNumber.Length > 0)
                {
                    //we need to do it on campaign Numer
                    using (var db = new NovaDBContext())
                    {

                        //lets do it differently here
                        var query = from ImportedInstallationActions in db.Set<ImportedInstallationActions>().Where(x => x.ContractNumber.ToLower() == strikeRateMasterDetails.contractNumber && x.ScheduleDate >= strikeRateMasterDetails.startDate && x.ScheduleDate <= strikeRateMasterDetails.endDate)
                                    join IRCodeS in db.Set<IRCodes>()
                                        on ImportedInstallationActions.IRCode equals IRCodeS.IRCodeName
                                    select new { ImportedInstallationActions, IRCodeS }
                                    ;

                        //now we have it joined

                        strikeRateReport.Name = query.Select(x => x.ImportedInstallationActions.Category).FirstOrDefault().ToString() + " - " + query.Select(x => x.ImportedInstallationActions.MyMobilityCategory).FirstOrDefault().ToString();
                        //strikeRateReport.Route = strikeRateMasterDetails.team;
                        strikeRateReport.Type = "Contract";
                        strikeRateReport.ContractNumber = strikeRateMasterDetails.contractNumber;
                        strikeRateReport.NoOfStoresSelected = query.Count();

                        var allWithAddNotUpIRCode = query.Where(x => (x.IRCodeS.IRCodeName.ToLower().Contains("anu") || (x.IRCodeS.IRCodeName.ToLower().Contains("add not up")))).ToList();
                        var allWithAddUpIrCode = query.Where(x => (!x.IRCodeS.IRCodeName.ToLower().Contains("anu") && (!x.IRCodeS.IRCodeName.ToLower().Contains("add not up")))).ToList();

                        //ok, let us now do calculations here
                        strikeRateReport.NoOfNonInstallations = allWithAddNotUpIRCode.Count();
                        strikeRateReport.NoOfAddsUp = strikeRateReport.NoOfStoresSelected - strikeRateReport.NoOfNonInstallations;

                        //ok, now we need to do it for the strike rate though.
                        float NoOfNonInstallationForStrikeRate = allWithAddNotUpIRCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        float NoOfInstallationsForStrikeRate = allWithAddUpIrCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        float allForStrikeRate = query.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                        if (allForStrikeRate == 0)
                        {
                            allForStrikeRate = strikeRateReport.NoOfStoresSelected;
                        }
                        //lets calculate.
                        strikeRateReport.OverallCompliance = (decimal)(NoOfInstallationsForStrikeRate / allForStrikeRate) * 100;
                        //ok, we now have everything except total
                    }
                }
                else
                {
                    //noting selected, lets do it on everything
                }
            }
            catch (Exception ex)
            {
                //return StatusCode(500, ex.InnerException.Message);

            }

            //lets do the work here

            //Querying with LINQ to Entities 

            return strikeRateReport;
        }

        /// <summary>
        ///  lets send a full list here, and redo the whole thing at once i think.
        /// </summary>

        public List<StrikeRateReport> GenerateBulkStrikeRateReports(List<BulkMasterDetails> bulkStrikeRateReports)
        {
            List<StrikeRateReport> strikeRateReports = new List<StrikeRateReport>();

            foreach (BulkMasterDetails rateReports in bulkStrikeRateReports)
            {
                StrikeRateReport strikeRateReport = new StrikeRateReport();

                strikeRateReport.Route = rateReports.Team;
                strikeRateReport.ContractNumber = rateReports.ContractNumber;
                strikeRateReport.Type = "Team";
                strikeRateReport.NoOfStoresSelected = bulkStrikeRateReports.Where(x => x.Team == rateReports.Team && x.ContractNumber == rateReports.ContractNumber).Count();

                var allWithAddNotUpIRCode = bulkStrikeRateReports.Where(x => ((x.IRCodeS.IRCodeName.ToLower().Contains("anu") || (x.IRCodeS.IRCodeName.ToLower().Contains("add not up"))) && x.ContractNumber == rateReports.ContractNumber && x.Team == rateReports.Team)).ToList();
                var allWithAddUpIrCode = bulkStrikeRateReports.Where(x => (!x.IRCodeS.IRCodeName.ToLower().Contains("anu") && (!x.IRCodeS.IRCodeName.ToLower().Contains("add not up")) && x.ContractNumber == rateReports.ContractNumber && x.Team == rateReports.Team)).ToList();

                //ok, let us now do calculations here
                strikeRateReport.NoOfNonInstallations = allWithAddNotUpIRCode.Count();
                strikeRateReport.NoOfAddsUp = strikeRateReport.NoOfStoresSelected - strikeRateReport.NoOfNonInstallations;

                //ok, now we need to do it for the strike rate though.
                float NoOfNonInstallationForStrikeRate = allWithAddNotUpIRCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                float NoOfInstallationsForStrikeRate = allWithAddUpIrCode.Where(x => x.IRCodeS.countsForStrikeRate == true).Count();
                float allForStrikeRate = bulkStrikeRateReports.Where(x => x.IRCodeS.countsForStrikeRate == true && x.ContractNumber == rateReports.ContractNumber && x.Team == rateReports.Team).Count();
                if (allForStrikeRate == 0)
                {
                    allForStrikeRate = strikeRateReport.NoOfStoresSelected;
                }
                ////lets calculate.
                strikeRateReport.OverallCompliance = (decimal)(NoOfInstallationsForStrikeRate / allForStrikeRate) * 100;
                strikeRateReports.Add(strikeRateReport);
            }
            //lets do the work here


            return strikeRateReports;
        }



        [Route("GetContractNumbersFromInstallations")]
        [HttpGet]
        public IEnumerable<DetailsForPictureReport> GetContractNumbersFromInstallations()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                List<DetailsForPictureReport> returnList = new List<DetailsForPictureReport>();
                returnList.AddRange(context.InstallationScheduleArchived.Where(x => x.CampaignFinished == true).Select(x => new DetailsForPictureReport() { campaignNumber = x.JobNumber, region = x.Region, store = x.Store, chain = x.Chain })
               .ToList().Distinct());

                returnList.AddRange(context.InstallationScheduleCurrent.Where(x => x.CampaignFinished == true).Select(x => new DetailsForPictureReport() { campaignNumber = x.JobNumber, region = x.Region, store = x.Store, chain = x.Chain })
               .ToList().Distinct());

                return returnList.Distinct().OrderBy(x => x.campaignNumber);
            }

        }


        [Route("GetContractNumberOnly")]
        [HttpGet]
        public IEnumerable<string> GetContractNumberOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                List<string> lstContractNumbers = new List<string>();

                lstContractNumbers.AddRange(context.InstallationScheduleCurrent.Where(s => s.FinishedDate > DateTime.Now.AddMonths(-3)).Select(x => x.JobNumber)
               .ToList().Distinct());


                // lstContractNumbers.AddRange(context.InstallationScheduleArchived.Select(x => x.JobNumber)
                //.ToList().Distinct());

                return lstContractNumbers.Distinct().OrderBy(x => x);
            }

        }

        [Route("GetStoresOnly")]
        [HttpGet]
        public IEnumerable<string> GetStoresOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                List<string> lstStoreName = new List<string>();


                lstStoreName.AddRange(context.InstallationScheduleCurrent.Select(x => x.Store)
               .Distinct().ToList());

                lstStoreName.AddRange(context.InstallationScheduleArchived.Select(x => x.Store)
                .Distinct().ToList());

                return lstStoreName.Distinct().OrderBy(x => x);
            }

        }


        [Route("GetChainOnly")]
        [HttpGet]
        public IEnumerable<string> GetChainOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                List<string> lstContractNumbers = new List<string>();

                lstContractNumbers.AddRange(context.InstallationScheduleCurrent.Select(x => x.Chain)
               .ToList().Distinct());

                lstContractNumbers.AddRange(context.InstallationScheduleArchived.Select(x => x.Chain)
                .ToList().Distinct());

                return lstContractNumbers.Distinct().OrderBy(x => x);
            }

        }

        [Route("GetRegionOnly")]
        [HttpGet]
        public IEnumerable<string> GetRegionOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                List<string> lstRegions = new List<string>();

                lstRegions.AddRange(context.InstallationScheduleCurrent.Select(x => x.Region)
               .ToList().Distinct());

                lstRegions.AddRange(context.InstallationScheduleArchived.Select(x => x.Region)
                 .ToList().Distinct());

                return lstRegions.Distinct().OrderBy(x => x);
            }

        }

        [Route("GetIrCodeOnly")]
        [HttpGet]
        public IEnumerable<string> GetIrCodeOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                List<string> lstIRCodes = new List<string>();

                lstIRCodes.AddRange(context.InstallationScheduleCurrent.Include(x => x.SelectedIRCode).Where(x => x.IRCodeID == x.SelectedIRCode.IRCodeID).Select(x => x.SelectedIRCode.IRCodeName)
               .ToList().Distinct());

                lstIRCodes.AddRange(context.InstallationScheduleArchived.Include(x => x.SelectedIRCode).Where(x => x.IRCodeID == x.SelectedIRCode.IRCodeID).Select(x => x.SelectedIRCode.IRCodeName)
                .ToList().Distinct());

                return lstIRCodes.Distinct().OrderBy(x => x);
            }

        }

        [Route("GetMediaOnly")]
        [HttpGet]
        public IEnumerable<string> GetMediaOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                List<string> lstMedia = new List<string>();

                lstMedia.AddRange(context.InstallationScheduleCurrent.Include(x => x.Media).Select(x => x.Media.MediaName)
               .ToList().Distinct());

                lstMedia.AddRange(context.InstallationScheduleArchived.Include(x => x.Media).Select(x => x.Media.MediaName)
              .ToList().Distinct());

                return lstMedia.Distinct().OrderBy(x => x);

            }

        }

        [Route("GetActionOnly")]
        [HttpGet]
        public IEnumerable<string> GetActionOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                List<string> lstAction = new List<string>();

                lstAction.AddRange(context.InstallationScheduleCurrent.Select(x => x.Status)
               .ToList().Distinct());

                lstAction.AddRange(context.InstallationScheduleArchived.Select(x => x.Status)
              .ToList().Distinct());

                return lstAction.Distinct().OrderBy(x => x);

            }

        }

        [Route("GetCategoryOnly")]
        [HttpGet]
        public IEnumerable<string> GetCategoryOnly()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                List<string> lstCategory = new List<string>();

                lstCategory.AddRange(context.InstallationScheduleCurrent.Select(x => x.CategoryName)
               .ToList().Distinct());

                lstCategory.AddRange(context.InstallationScheduleArchived.Select(x => x.CategoryName)
              .ToList().Distinct());

                return lstCategory.Distinct().OrderBy(x => x);

            }

        }


        [Route("GetDetailsForPictureReport")]
        [HttpGet]
        public IEnumerable<DetailsForPictureReport> GetDetailsForPictureReport()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                try
                {
                    List<DetailsForPictureReport> lstDetailsForPictureReports = new List<DetailsForPictureReport>();


                    lstDetailsForPictureReports.AddRange(context.InstallationScheduleCurrent.Where(x => x.CampaignFinished == true).Select(x => new DetailsForPictureReport() { campaignNumber = x.JobNumber, region = x.Region, store = x.Store, chain = x.Chain })
                    .ToList());

                    lstDetailsForPictureReports.AddRange(context.InstallationScheduleArchived.Where(x => x.CampaignFinished == true).Select(x => new DetailsForPictureReport() { campaignNumber = x.JobNumber, region = x.Region, store = x.Store, chain = x.Chain })
                    .ToList());

                    return lstDetailsForPictureReports.Distinct().OrderBy(x => x.campaignNumber);
                }
                catch (Exception ex)
                {
                    return null;
                }

            }

        }

        /// <summary>
        /// TODO Jacques
        /// This has renamed from   [Route("GetInstallationActionsByFilter")] to   [Route("GetInstallationActionsByFilterOld")]
        /// The reason for the change is that sales and support should only use the provided url http://192.168.0.13:9821/
        /// </summary>
        /// <param name="strikeRateMasterDetails"></param>ff
        [Route("GetInstallationActionsByFilter_Old")]
        [HttpPost]
        public IEnumerable<InstallationActionPictureReport> GetInstallationActionsByFilter(StrikeRateMasterDetails strikeRateMasterDetails)
        {
            //Removed all the try catch because whats the point of doing them if the catch does nothing

            var context = new NovaDBContext();

            IEnumerable<InstallationActionPictureReport> results;

            string startDateFilter = "";
            string endDateFilter = "";
            //var firstResults =  context.InstallationScheduleCurrent.Include(x => x.SelectedIRCode).Include(x => x.InstallationTeam).Include(x => x.Media).Include(x => x.SelectedIRCode).Where(x => x.CampaignFinished == true).ToList();

            if (strikeRateMasterDetails.endDate != null && strikeRateMasterDetails.endDate.ToString() != "")
            {
                strikeRateMasterDetails.endDate = strikeRateMasterDetails.endDate.Value.AddDays(1);
            }

            if (strikeRateMasterDetails.startDate.ToString() != "" && strikeRateMasterDetails.startDate != null && strikeRateMasterDetails.endDate.ToString() != "" && strikeRateMasterDetails.endDate != null)
            {
                startDateFilter = strikeRateMasterDetails.startDate.Value.ToString("MMM dd , yyyy");
                endDateFilter = strikeRateMasterDetails.startDate.Value.ToString("MMM dd , yyyy");

                results = context.InstallationScheduleCurrent.Include(x => x.InstallationTeam).
                    Include(x => x.Media).Where(x => x.CampaignFinished == true && x.OpenedDate >= strikeRateMasterDetails.startDate && x.FinishedDate <= strikeRateMasterDetails.endDate).
                    Select(x => new InstallationActionPictureReport
                    {
                        actionId = x.InstallationScheduleCurrentID.
                    ToString(),
                        Region = x.Region,
                        Chain = x.Chain,
                        Store = x.Store,
                        Contract = x.JobNumber,
                        Team = x.InstallationTeam.InstallationTeamName,
                        Category = x.CategoryName,
                        Product = x.Product,
                        MediaType = x.Media.MediaName,
                        Action = x.Status,
                        IRCode = x.SelectedIRCode.IRCodeName,
                        IRCodeComment = x.IRCodeComment,
                        ScheduleDate = Convert.ToDateTime(x.ForDate),
                        DateClosed = x.FinishedDate,
                        DateOpened = x.OpenedDate,
                        ImageURL = x.imageURL
                    });
            }
            else
            {
                results = context.InstallationScheduleCurrent.Include(x => x.SelectedIRCode).Include(x => x.InstallationTeam).Include(x => x.Media).Include(x => x.SelectedIRCode).Where(x => x.CampaignFinished == true).Select(x => new InstallationActionPictureReport { actionId = x.InstallationScheduleCurrentID.ToString(), Region = x.Region, Chain = x.Chain, Store = x.Store, Contract = x.JobNumber, Team = x.InstallationTeam.InstallationTeamName, Category = x.CategoryName, Product = x.Product, MediaType = x.Media.MediaName, Action = x.Status, IRCode = x.SelectedIRCode.IRCodeName, IRCodeComment = x.IRCodeComment, ScheduleDate = Convert.ToDateTime(x.ForDate), DateClosed = x.FinishedDate, DateOpened = x.OpenedDate, ImageURL = x.imageURL, IRCodeID = x.IRCodeID });
            }

            if (strikeRateMasterDetails.contractNumbersSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.contractNumbersSelected.Select(p => p);
                results = results.Where(v => filterValue.Contains(v.Contract.ToString()));
            }

            if (strikeRateMasterDetails.startDate.ToString() != "" && strikeRateMasterDetails.startDate != null)
            {
                results = results.Where(x => x.ScheduleDate >= strikeRateMasterDetails.startDate || x.DateClosed >= strikeRateMasterDetails.startDate);
            }
            if (strikeRateMasterDetails.endDate.ToString() != "" && strikeRateMasterDetails.endDate != null)
            {
                results = results.Where(x => x.ScheduleDate <= strikeRateMasterDetails.endDate || x.DateClosed <= strikeRateMasterDetails.endDate);
            }

            if (strikeRateMasterDetails.storesSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.storesSelected.Select(p => p);
                results = results.Where(v => filterValue.Contains(v.Store.ToString()));
            }
            if (strikeRateMasterDetails.chainsSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.chainsSelected.Select(p => p);
                results = results.Where(v => filterValue.Contains(v.Chain.ToString()));
            }

            if (strikeRateMasterDetails.regionsSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.regionsSelected.Select(p => p);
                results = results.Where(v => filterValue.Contains(v.Region.ToString()));
            }

            if (strikeRateMasterDetails.irCodesSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.irCodesSelected.Select(p => p);
                var irCodeIDS = context.IRCodes.Where(x => filterValue.Contains(x.IRCodeName)).Select(x => x.IRCodeID).ToList();
                results = results.Where(v => irCodeIDS.Contains(v.IRCodeID));
            }

            if (strikeRateMasterDetails.mediaSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.mediaSelected.Select(p => p);
                var mediaNames = context.Media.Where(x => filterValue.Contains(x.MediaName)).Select(x => x.MediaName).ToList();
                results = results.Where(v => filterValue.Contains(v.MediaType.ToString()));
            }

            if (strikeRateMasterDetails.actionSelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.actionSelected.Select(p => p);
                results = results.Where(v => filterValue.Contains(v.Action.ToString()));
            }

            if (strikeRateMasterDetails.categorySelected.Count > 0)
            {
                var filterValue = strikeRateMasterDetails.categorySelected.Select(p => p);
                results = results.Where(v => filterValue.Contains(v.Category.ToString()));
            }

            if (strikeRateMasterDetails.includePictureURL)
            {
                string host = "pcaimages.primeinstore.co.za";
                //string host = HttpContext.Request.Host.Value;
                host = "https://" + host;
                host = host + "/api";
                host = host + "/PhotoHanler";
                host = host + "/GetImagesLQ";
                host = host + "/?imageURL=";


                string imageURLWithName = host;

                try
                {
                    results = results.ToList();
                }
                catch (Exception ex)
                {

                    throw;
                }

                foreach (var InstallationActionPictureReport in results)
                {
                    if (String.IsNullOrEmpty(InstallationActionPictureReport.ImageURL))
                    {
                        string[] fileEntries = Directory.GetFiles(Path.Combine(pathForImages, InstallationActionPictureReport.Contract));
                        string pathForPicture = "";
                        DateTime modifiedDate = new DateTime();
                        foreach (string file in fileEntries)
                        {
                            if (file.Contains(InstallationActionPictureReport.actionId.ToLower()))
                            {
                                FileInfo fi = new FileInfo(file);
                                if (fi.LastWriteTime >= modifiedDate)
                                {
                                    modifiedDate = fi.LastWriteTime;
                                    pathForPicture = "";
                                    //this must be it
                                    pathForPicture = host + InstallationActionPictureReport.Contract + @"\";
                                    pathForPicture = host + file.Replace(pathForImages, "");
                                    InstallationActionPictureReport.ImageURL = pathForPicture;
                                }


                                //context.InstallationScheduleCurrent.Update(InstallationActionPictureReport);
                            }
                        }
                    }
                    else
                    {
                        InstallationActionPictureReport.ImageURL = InstallationActionPictureReport.ImageURL.Replace("GetImages", "GetImagesLQ");
                    }
                }

            }

           //var returnData = results.ToList(); results.ToList();

            //var picData = results.FirstOrDefault(s => s.ImageURL != null);

            return results;



        }

        [Route("GetMerchandisingActionsByFilter")]
        [HttpPost]
        public IEnumerable<MerchandisingReportResults> GetMerchandisingActionsByFilter(StrikeRateMasterDetails strikeRateMasterDetails)
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                IEnumerable<InstallationActionPictureReport> results = context.InstallationScheduleCurrent
                    .Include(x => x.SelectedIRCode).Include(x => x.InstallationTeam).Include(x => x.Media).ThenInclude(x => x.Questions).ThenInclude(x => x.QuestionsAndAnswers).ThenInclude(x => x.Answers)
                    .Include(x => x.SelectedIRCode)
                    //.Include(x => x.InstallationScheduleQuestionsAndAnswers)
                    .Where(x => x.CampaignFinished == true).Select(x => new InstallationActionPictureReport { actionId = x.InstallationScheduleCurrentID.ToString(), Region = x.Region, Chain = x.Chain, Store = x.Store, Contract = x.JobNumber, Team = x.InstallationTeam.InstallationTeamName, Category = x.CategoryName, Product = x.Product, MediaType = x.Media.MediaName, Action = x.Status, IRCode = x.SelectedIRCode.IRCodeName, IRCodeComment = x.IRCodeComment, ScheduleDate = Convert.ToDateTime(x.ForDate), DateClosed = x.FinishedDate, DateOpened = x.OpenedDate, ImageURL = x.imageURL, IRCodeID = x.IRCodeID });


                IEnumerable<InstallationActionPictureReport> resultsArchived = context.InstallationScheduleArchived
                   .Include(x => x.SelectedIRCode).Include(x => x.InstallationTeam).Include(x => x.Media).ThenInclude(x => x.Questions).ThenInclude(x => x.QuestionsAndAnswers).ThenInclude(x => x.Answers)
                   .Include(x => x.SelectedIRCode)
                   //.Include(x => x.InstallationScheduleQuestionsAndAnswers)
                   .Where(x => x.CampaignFinished == true).Select(x => new InstallationActionPictureReport { actionId = x.InstallationScheduleCurrentID.ToString(), Region = x.Region, Chain = x.Chain, Store = x.Store, Contract = x.JobNumber, Team = x.InstallationTeam.InstallationTeamName, Category = x.CategoryName, Product = x.Product, MediaType = x.Media.MediaName, Action = x.Status, IRCode = x.SelectedIRCode.IRCodeName, IRCodeComment = x.IRCodeComment, ScheduleDate = Convert.ToDateTime(x.ForDate), DateClosed = x.FinishedDate, DateOpened = x.OpenedDate, ImageURL = x.imageURL, IRCodeID = x.IRCodeID });

                //    .Where(x => x.Contract == strikeRateMasterDetails.contractNumber)
                //.Distinct().ToList().OrderBy(x => x.Region);
                //we will need to find the questions and aswers for it

                if (strikeRateMasterDetails.contractNumbersSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.contractNumbersSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Contract.ToString()));
                    //results = results.Where(x => x.Contract == strikeRateMasterDetails.contractNumber);
                }

                if (strikeRateMasterDetails.startDate.ToString() != "" && strikeRateMasterDetails.startDate != null)
                {
                    results = results.Where(x => x.ScheduleDate >= strikeRateMasterDetails.startDate);
                }
                if (strikeRateMasterDetails.endDate.ToString() != "" && strikeRateMasterDetails.endDate != null)
                {
                    results = results.Where(x => x.ScheduleDate <= strikeRateMasterDetails.endDate);
                }

                if (strikeRateMasterDetails.storesSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.storesSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Store.ToString()));

                }
                if (strikeRateMasterDetails.chainsSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.chainsSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Chain.ToString()));
                }

                if (strikeRateMasterDetails.regionsSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.regionsSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Region.ToString()));
                }

                if (strikeRateMasterDetails.irCodesSelected.Count > 0)
                {
                    //lets get the ircode ids here
                    //results = results.Where(x => x.IRCode.Intersect(strikeRateMasterDetails.irCodesSelected));
                    var filterValue = strikeRateMasterDetails.irCodesSelected.Select(p => p);

                    var irCodeIDS = context.IRCodes.Where(x => filterValue.Contains(x.IRCodeName)).Select(x => x.IRCodeID).ToList();
                    results = results.Where(v => irCodeIDS.Contains(v.IRCodeID));
                }

                if (strikeRateMasterDetails.irCodes != null)
                {
                    //var selectedIRCodes = context.IRCodes.Select(x => x.IRCodeID).ToList().Contains(strikeRateMasterDetails.irCodes.Select(y => y.IRCodeID).ToList());
                }
                //if (strikeRateMasterDetails.contractNumber != "")
                //{
                //    results = results.Where(x => x.Contract == strikeRateMasterDetails.contractNumber);
                //}

                //if (strikeRateMasterDetails.contractNumber != "")
                //{
                //    results = results.Where(x => x.Contract == strikeRateMasterDetails.contractNumber);
                //}
                results = results.Distinct().ToList().OrderBy(x => x.Region);


                List<QuestionsAndAnswers> qanda = context.QuestionsAndAnswers.Include(x => x.Questions).Include(x => x.Answers).ToList();
                List<MerchandisingReportResults> merchandisingReportResults = new List<MerchandisingReportResults>();
                foreach (var item in results)
                {
                    MerchandisingReportResults MerchandisingReportResults = new MerchandisingReportResults();
                    //lets populate questions and ansers
                    var myQandA = context.InstallationScheduleQuestionsAndAnswers.Where(x => x.InstallationActionId.ToString() == item.actionId);
                    foreach (InstallationScheduleQuestionsAndAnswers scheduleQuestionsAndAnswers in myQandA)
                    {
                        //get the right question and answer
                        var finalQA = qanda.Where(x => x.Questions.QuestionId == scheduleQuestionsAndAnswers.QuestionId).FirstOrDefault();
                        var finalAnswer = qanda.Where(x => x.AnswerId == scheduleQuestionsAndAnswers.AnswerId).FirstOrDefault();
                        if (finalQA.Questions.Quetion == "Are all lines on shelf and neat?")
                        {
                            MerchandisingReportResults.linesNeat = finalAnswer.Answers.Answer;
                            if (scheduleQuestionsAndAnswers.Comment != null && scheduleQuestionsAndAnswers.Comment.ToString() != "")
                            {
                                MerchandisingReportResults.linesNeat += " : " + scheduleQuestionsAndAnswers.Comment;
                            }
                        }
                        if (finalQA.Questions.Quetion == "Are all lines priced?")
                        {
                            MerchandisingReportResults.pricedCorrect = finalAnswer.Answers.Answer;
                            if (scheduleQuestionsAndAnswers.Comment != null && scheduleQuestionsAndAnswers.Comment.ToString() != "")
                            {
                                MerchandisingReportResults.pricedCorrect += " : " + scheduleQuestionsAndAnswers.Comment;
                            }
                        }
                        if (finalQA.Questions.Quetion == "Are the stock counts on the system correct")
                        {
                            MerchandisingReportResults.stockCounts = finalAnswer.Answers.Answer;
                            if (scheduleQuestionsAndAnswers.Comment != null && scheduleQuestionsAndAnswers.Comment.ToString() != "")
                            {
                                MerchandisingReportResults.stockCounts += " : " + scheduleQuestionsAndAnswers.Comment;
                            }
                        }
                        if (finalQA.Questions.Quetion == "Did you pull a “dispo” report in-store?")
                        {
                            MerchandisingReportResults.isDispo = finalAnswer.Answers.Answer;
                            if (scheduleQuestionsAndAnswers.Comment != null && scheduleQuestionsAndAnswers.Comment.ToString() != "")
                            {
                                MerchandisingReportResults.isDispo += " : " + scheduleQuestionsAndAnswers.Comment;
                            }
                        }
                        if (finalQA.Questions.Quetion == "Was there stock in the store room, if so did you pack it onto the shelf?")
                        {
                            MerchandisingReportResults.stockRoomStock = finalAnswer.Answers.Answer;
                            if (scheduleQuestionsAndAnswers.Comment != null && scheduleQuestionsAndAnswers.Comment.ToString() != "")
                            {
                                MerchandisingReportResults.stockRoomStock += " : " + scheduleQuestionsAndAnswers.Comment;
                            }
                        }
                        if (finalQA.Questions.Quetion == "Did the store manager assist with aged stock?")
                        {
                            MerchandisingReportResults.stockAged = finalAnswer.Answers.Answer;
                            if (scheduleQuestionsAndAnswers.Comment != null && scheduleQuestionsAndAnswers.Comment.ToString() != "")
                            {
                                MerchandisingReportResults.stockAged += " : " + scheduleQuestionsAndAnswers.Comment;
                            }
                        }
                    }
                    MerchandisingReportResults.actionId = item.actionId;
                    MerchandisingReportResults.Category = item.Category;
                    MerchandisingReportResults.Chain = item.Chain;
                    MerchandisingReportResults.Contract = item.Contract;
                    MerchandisingReportResults.MediaType = item.MediaType;
                    MerchandisingReportResults.Region = item.Region;
                    MerchandisingReportResults.Team = item.Team;
                    MerchandisingReportResults.Store = item.Store;
                    MerchandisingReportResults.Contract = item.Contract;
                    MerchandisingReportResults.ImageURL = item.ImageURL;
                    MerchandisingReportResults.Product = item.Product;
                    MerchandisingReportResults.Action = item.Action;
                    MerchandisingReportResults.IRCodeComment = item.IRCodeComment;
                    MerchandisingReportResults.IRCode = item.IRCode;

                    if (strikeRateMasterDetails.includePictureURL)
                    {
                        string host = "pcaimages.primeinstore.co.za";
                        //string host = HttpContext.Request.Host.Value;
                        host = "https://" + host;
                        host = host + "/api";
                        host = host + "/PhotoHanler";
                        host = host + "/GetImagesLQ";
                        host = host + "/?imageURL=";


                        if (MerchandisingReportResults.ImageURL.ToString() == "")
                        {
                            try
                            {
                                string[] fileEntries = Directory.GetFiles(Path.Combine(pathForImages, MerchandisingReportResults.Contract));
                                string pathForPicture = "";
                                string[] dirs = Directory.GetFiles(Path.Combine(pathForImages, MerchandisingReportResults.Contract), MerchandisingReportResults.actionId.ToLower() + "*");

                                foreach (string file in dirs)
                                {
                                    if (file.Contains(MerchandisingReportResults.actionId.ToLower()))
                                    {
                                        MerchandisingReportResults MerchandisingWithPicture = new MerchandisingReportResults();
                                        //MerchandisingWithPicture = MerchandisingReportResults;

                                        MerchandisingWithPicture.actionId = item.actionId;
                                        MerchandisingWithPicture.Category = item.Category;
                                        MerchandisingWithPicture.Chain = item.Chain;
                                        MerchandisingWithPicture.Contract = item.Contract;
                                        MerchandisingWithPicture.MediaType = item.MediaType;
                                        MerchandisingWithPicture.Region = item.Region;
                                        MerchandisingWithPicture.Team = item.Team;
                                        MerchandisingWithPicture.Store = item.Store;
                                        MerchandisingWithPicture.Contract = item.Contract;
                                        MerchandisingWithPicture.ImageURL = item.ImageURL;
                                        MerchandisingWithPicture.Product = item.Product;
                                        MerchandisingWithPicture.ScheduleDate = item.ScheduleDate;
                                        MerchandisingWithPicture.DateOpened = item.DateClosed;
                                        MerchandisingWithPicture.DateClosed = item.DateClosed;
                                        MerchandisingWithPicture.Action = item.Action;
                                        MerchandisingWithPicture.IRCodeComment = item.IRCodeComment;
                                        MerchandisingWithPicture.IRCode = item.IRCode;
                                        MerchandisingWithPicture.stockRoomStock = MerchandisingReportResults.stockRoomStock;
                                        MerchandisingWithPicture.isDispo = MerchandisingReportResults.isDispo;
                                        MerchandisingWithPicture.stockCounts = MerchandisingReportResults.stockCounts;
                                        MerchandisingWithPicture.pricedCorrect = MerchandisingReportResults.pricedCorrect;
                                        MerchandisingWithPicture.linesNeat = MerchandisingReportResults.linesNeat;


                                        MerchandisingWithPicture.actionId = file;
                                        pathForPicture = "";
                                        //this must be it
                                        pathForPicture = host + MerchandisingWithPicture.Contract + @"\";
                                        pathForPicture = host + file.Replace(pathForImages, "");
                                        MerchandisingWithPicture.ImageURL = pathForPicture;
                                        merchandisingReportResults.Add(MerchandisingWithPicture);
                                        //context.InstallationScheduleCurrent.Update(InstallationActionPictureReport);
                                    }
                                }
                                if (dirs.Length == 0)
                                {
                                    merchandisingReportResults.Add(MerchandisingReportResults);
                                }
                            }

                            catch
                            {

                            }

                        }
                        else
                        {
                            //MerchandisingReportResults.ImageURL = MerchandisingReportResults.ImageURL.Replace("GetImages", "GetImagesLQ");
                        }

                        //lets get the image for this action




                        //lets get the path of the image, and then return the image as well


                    }

                    //merchandisingReportResults.Add(MerchandisingReportResults);
                }
                //lets see if we can generate the excel file here





                return merchandisingReportResults;
            }

        }



        [Route("GetMerchandisingActionsByFilterDynamic")]
        [HttpPost]
        public IEnumerable<MerchandisingReportResultsDynamic> GetMerchandisingActionsByFilterDynamic(StrikeRateMasterDetails strikeRateMasterDetails)
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                IEnumerable<InstallationActionPictureReport> results = context.InstallationScheduleCurrent
                    .Include(x => x.SelectedIRCode).Include(x => x.InstallationTeam).Include(x => x.Media).ThenInclude(x => x.Questions).ThenInclude(x => x.QuestionsAndAnswers).ThenInclude(x => x.Answers)
                    .Include(x => x.SelectedIRCode)
                    //.Include(x => x.InstallationScheduleQuestionsAndAnswers)
                    .Where(x => x.CampaignFinished == true).Select(x => new InstallationActionPictureReport { actionId = x.InstallationScheduleCurrentID.ToString(), Region = x.Region, Chain = x.Chain, Store = x.Store, Contract = x.JobNumber, Team = x.InstallationTeam.InstallationTeamName, Category = x.CategoryName, Product = x.Product, MediaType = x.Media.MediaName, Action = x.Status, IRCode = x.SelectedIRCode.IRCodeName, IRCodeComment = x.IRCodeComment, ScheduleDate = Convert.ToDateTime(x.ForDate), DateClosed = x.FinishedDate, DateOpened = x.OpenedDate, ImageURL = x.imageURL, IRCodeID = x.IRCodeID });


                IEnumerable<InstallationActionPictureReport> resultsArchived = context.InstallationScheduleArchived
                   .Include(x => x.SelectedIRCode).Include(x => x.InstallationTeam).Include(x => x.Media).ThenInclude(x => x.Questions).ThenInclude(x => x.QuestionsAndAnswers).ThenInclude(x => x.Answers)
                   .Include(x => x.SelectedIRCode)
                   //.Include(x => x.InstallationScheduleQuestionsAndAnswers)
                   .Where(x => x.CampaignFinished == true).Select(x => new InstallationActionPictureReport { actionId = x.InstallationScheduleCurrentID.ToString(), Region = x.Region, Chain = x.Chain, Store = x.Store, Contract = x.JobNumber, Team = x.InstallationTeam.InstallationTeamName, Category = x.CategoryName, Product = x.Product, MediaType = x.Media.MediaName, Action = x.Status, IRCode = x.SelectedIRCode.IRCodeName, IRCodeComment = x.IRCodeComment, ScheduleDate = Convert.ToDateTime(x.ForDate), DateClosed = x.FinishedDate, DateOpened = x.OpenedDate, ImageURL = x.imageURL, IRCodeID = x.IRCodeID });

                //    .Where(x => x.Contract == strikeRateMasterDetails.contractNumber)
                //.Distinct().ToList().OrderBy(x => x.Region);
                //we will need to find the questions and aswers for it

                if (strikeRateMasterDetails.contractNumbersSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.contractNumbersSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Contract.ToString()));
                    //results = results.Where(x => x.Contract == strikeRateMasterDetails.contractNumber);
                }

                if (strikeRateMasterDetails.startDate.ToString() != "" && strikeRateMasterDetails.startDate != null)
                {
                    results = results.Where(x => x.ScheduleDate >= strikeRateMasterDetails.startDate);
                }
                if (strikeRateMasterDetails.endDate.ToString() != "" && strikeRateMasterDetails.endDate != null)
                {
                    results = results.Where(x => x.ScheduleDate <= strikeRateMasterDetails.endDate);
                }

                if (strikeRateMasterDetails.storesSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.storesSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Store.ToString()));

                }
                if (strikeRateMasterDetails.chainsSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.chainsSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Chain.ToString()));
                }

                if (strikeRateMasterDetails.regionsSelected.Count > 0)
                {
                    var filterValue = strikeRateMasterDetails.regionsSelected.Select(p => p);
                    results = results.Where(v => filterValue.Contains(v.Region.ToString()));
                }

                if (strikeRateMasterDetails.irCodesSelected.Count > 0)
                {
                    //lets get the ircode ids here
                    //results = results.Where(x => x.IRCode.Intersect(strikeRateMasterDetails.irCodesSelected));
                    var filterValue = strikeRateMasterDetails.irCodesSelected.Select(p => p);

                    var irCodeIDS = context.IRCodes.Where(x => filterValue.Contains(x.IRCodeName)).Select(x => x.IRCodeID).ToList();
                    results = results.Where(v => irCodeIDS.Contains(v.IRCodeID));
                }

                if (strikeRateMasterDetails.irCodes != null)
                {
                    //var selectedIRCodes = context.IRCodes.Select(x => x.IRCodeID).ToList().Contains(strikeRateMasterDetails.irCodes.Select(y => y.IRCodeID).ToList());
                }
                //if (strikeRateMasterDetails.contractNumber != "")
                //{
                //    results = results.Where(x => x.Contract == strikeRateMasterDetails.contractNumber);
                //}

                //if (strikeRateMasterDetails.contractNumber != "")
                //{
                //    results = results.Where(x => x.Contract == strikeRateMasterDetails.contractNumber);
                //}
                results = results.Distinct().ToList().OrderBy(x => x.Region);


                List<QuestionsAndAnswers> qanda = context.QuestionsAndAnswers.Include(x => x.Questions).Include(x => x.Answers).ToList();
                List<MerchandisingReportResultsDynamic> merchandisingReportResults = new List<MerchandisingReportResultsDynamic>();
                foreach (var item in results)
                {
                    MerchandisingReportResultsDynamic MerchandisingReportResults = new MerchandisingReportResultsDynamic();
                    MerchandisingReportResults.AnswerForReports = new List<QuestionAndAnswerForReport>();
                    //lets populate questions and ansers
                    var myQandA = context.InstallationScheduleQuestionsAndAnswers.Where(x => x.InstallationActionId.ToString() == item.actionId);
                    foreach (InstallationScheduleQuestionsAndAnswers scheduleQuestionsAndAnswers in myQandA)
                    {
                        //get the right question and answer
                        var finalQA = qanda.Where(x => x.Questions.QuestionId == scheduleQuestionsAndAnswers.QuestionId).FirstOrDefault();
                        var finalAnswer = qanda.Where(x => x.AnswerId == scheduleQuestionsAndAnswers.AnswerId).FirstOrDefault();


                        QuestionAndAnswerForReport questionAndAnswerForReport = new QuestionAndAnswerForReport();
                        questionAndAnswerForReport.Answer = finalAnswer.Answers.Answer;
                        if (scheduleQuestionsAndAnswers.Comment != null)
                        {
                            questionAndAnswerForReport.Comment = scheduleQuestionsAndAnswers.Comment;
                        }
                        questionAndAnswerForReport.Question = finalQA.Questions.Quetion;
                        MerchandisingReportResults.AnswerForReports.Add(questionAndAnswerForReport);
                        //if (finalQA.Questions.Quetion == "Are all lines on shelf and neat?")
                        //{
                        //    MerchandisingReportResults.linesNeat = finalAnswer.Answers.Answer;
                        //    if (scheduleQuestionsAndAnswers.Comment.ToString() != "")
                        //    {
                        //        MerchandisingReportResults.linesNeat += " : " + scheduleQuestionsAndAnswers.Comment;
                        //    }
                        //}
                        //if (finalQA.Questions.Quetion == "Are all lines priced?")
                        //{
                        //    MerchandisingReportResults.pricedCorrect = finalAnswer.Answers.Answer;
                        //    if (scheduleQuestionsAndAnswers.Comment.ToString() != "")
                        //    {
                        //        MerchandisingReportResults.pricedCorrect += " : " + scheduleQuestionsAndAnswers.Comment;
                        //    }
                        //}
                        //if (finalQA.Questions.Quetion == "Are the stock counts on the system correct")
                        //{
                        //    MerchandisingReportResults.stockCounts = finalAnswer.Answers.Answer;
                        //    if (scheduleQuestionsAndAnswers.Comment.ToString() != "")
                        //    {
                        //        MerchandisingReportResults.stockCounts += " : " + scheduleQuestionsAndAnswers.Comment;
                        //    }
                        //}
                        //if (finalQA.Questions.Quetion == "Did you pull a “dispo” report in-store?")
                        //{
                        //    MerchandisingReportResults.isDispo = finalAnswer.Answers.Answer;
                        //    if (scheduleQuestionsAndAnswers.Comment.ToString() != "")
                        //    {
                        //        MerchandisingReportResults.isDispo += " : " + scheduleQuestionsAndAnswers.Comment;
                        //    }
                        //}
                        //if (finalQA.Questions.Quetion == "Was there stock in the store room, if so did you pack it onto the shelf?")
                        //{
                        //    MerchandisingReportResults.stockRoomStock = finalAnswer.Answers.Answer;
                        //    if (scheduleQuestionsAndAnswers.Comment.ToString() != "")
                        //    {
                        //        MerchandisingReportResults.stockRoomStock += " : " + scheduleQuestionsAndAnswers.Comment;
                        //    }
                        //}
                        //if (finalQA.Questions.Quetion == "Did the store manager assist with aged stock?")
                        //{
                        //    MerchandisingReportResults.stockAged = finalAnswer.Answers.Answer;
                        //    if (scheduleQuestionsAndAnswers.Comment.ToString() != "")
                        //    {
                        //        MerchandisingReportResults.stockAged += " : " + scheduleQuestionsAndAnswers.Comment;
                        //    }
                        //}
                    }
                    MerchandisingReportResults.actionId = item.actionId;
                    MerchandisingReportResults.Category = item.Category;
                    MerchandisingReportResults.Chain = item.Chain;
                    MerchandisingReportResults.Contract = item.Contract;
                    MerchandisingReportResults.MediaType = item.MediaType;
                    MerchandisingReportResults.Region = item.Region;
                    MerchandisingReportResults.Team = item.Team;
                    MerchandisingReportResults.Store = item.Store;
                    MerchandisingReportResults.Contract = item.Contract;
                    MerchandisingReportResults.ImageURL = item.ImageURL;
                    MerchandisingReportResults.Product = item.Product;
                    MerchandisingReportResults.Action = item.Action;
                    MerchandisingReportResults.IRCodeComment = item.IRCodeComment;
                    MerchandisingReportResults.IRCode = item.IRCode;

                    if (strikeRateMasterDetails.includePictureURL)
                    {
                        string host = "pcaimages.primeinstore.co.za";
                        //string host = HttpContext.Request.Host.Value;
                        host = "https://" + host;
                        host = host + "/api";
                        host = host + "/PhotoHanler";
                        host = host + "/GetImagesLQ";
                        host = host + "/?imageURL=";


                        if (MerchandisingReportResults.ImageURL.ToString() == "")
                        {
                            try
                            {
                                string[] fileEntries = Directory.GetFiles(Path.Combine(pathForImages, MerchandisingReportResults.Contract));
                                string pathForPicture = "";
                                string[] dirs = Directory.GetFiles(Path.Combine(pathForImages, MerchandisingReportResults.Contract), MerchandisingReportResults.actionId.ToLower() + "*");

                                foreach (string file in dirs)
                                {
                                    if (file.Contains(MerchandisingReportResults.actionId.ToLower()))
                                    {
                                        MerchandisingReportResultsDynamic MerchandisingWithPicture = new MerchandisingReportResultsDynamic();
                                        //MerchandisingWithPicture = MerchandisingReportResults;
                                        MerchandisingWithPicture.AnswerForReports = MerchandisingReportResults.AnswerForReports;
                                        MerchandisingWithPicture.actionId = item.actionId;
                                        MerchandisingWithPicture.Category = item.Category;
                                        MerchandisingWithPicture.Chain = item.Chain;
                                        MerchandisingWithPicture.Contract = item.Contract;
                                        MerchandisingWithPicture.MediaType = item.MediaType;
                                        MerchandisingWithPicture.Region = item.Region;
                                        MerchandisingWithPicture.Team = item.Team;
                                        MerchandisingWithPicture.Store = item.Store;
                                        MerchandisingWithPicture.Contract = item.Contract;
                                        MerchandisingWithPicture.ImageURL = item.ImageURL;
                                        MerchandisingWithPicture.Product = item.Product;
                                        MerchandisingWithPicture.ScheduleDate = item.ScheduleDate;
                                        MerchandisingWithPicture.DateOpened = item.DateClosed;
                                        MerchandisingWithPicture.DateClosed = item.DateClosed;
                                        MerchandisingWithPicture.Action = item.Action;
                                        MerchandisingWithPicture.IRCodeComment = item.IRCodeComment;
                                        MerchandisingWithPicture.IRCode = item.IRCode;
                                        MerchandisingWithPicture.stockRoomStock = MerchandisingReportResults.stockRoomStock;
                                        MerchandisingWithPicture.isDispo = MerchandisingReportResults.isDispo;
                                        MerchandisingWithPicture.stockCounts = MerchandisingReportResults.stockCounts;
                                        MerchandisingWithPicture.pricedCorrect = MerchandisingReportResults.pricedCorrect;
                                        MerchandisingWithPicture.linesNeat = MerchandisingReportResults.linesNeat;


                                        MerchandisingWithPicture.actionId = file;
                                        pathForPicture = "";
                                        //this must be it
                                        pathForPicture = host + MerchandisingWithPicture.Contract + @"\";
                                        pathForPicture = host + file.Replace(pathForImages, "");
                                        MerchandisingWithPicture.ImageURL = pathForPicture;
                                        merchandisingReportResults.Add(MerchandisingWithPicture);
                                        //context.InstallationScheduleCurrent.Update(InstallationActionPictureReport);
                                    }
                                }
                                if (dirs.Length == 0)
                                {
                                    merchandisingReportResults.Add(MerchandisingReportResults);
                                }
                            }

                            catch
                            {

                            }

                        }
                        else
                        {
                            //MerchandisingReportResults.ImageURL = MerchandisingReportResults.ImageURL.Replace("GetImages", "GetImagesLQ");
                        }

                        //lets get the image for this action




                        //lets get the path of the image, and then return the image as well


                    }

                    //merchandisingReportResults.Add(MerchandisingReportResults);
                }
                //lets see if we can generate the excel file here





                return merchandisingReportResults;
            }

        }



        [Route("GetInstallationsCounts")]
        [HttpPost]
        public ActionResult<object> GetInstallationsCounts(string emailToSendTo)
        {
            HelperController helper = new HelperController();
            HelperController.GenerateExcelFileData generateExcelFileData = new HelperController.GenerateExcelFileData();
            generateExcelFileData.emailToSendTo = emailToSendTo;

            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                var myResults =
                    context.InstallationCounts.FromSqlRaw("select  it.InstallationTeamName as installationTeamName,  count(ic.InstallationTeamID) as TotalActions "
                    + " , (Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 1   and icc.IsCurrent = 1) as ActionsCompleted "
                    + " ,(Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 0    and icc.IsCurrent = 1 ) as ActionsLeft "
                     + " ,(Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 1    and icc.IsCurrent = 1 and icc.FinishedDate >= '" + System.DateTime.Now.Date.ToString("yyyy/MM/dd") + "' ) as ActionsCompletedForTheDay "
                          + " ,Cast(Cast((Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 1   and icc.IsCurrent = 1 ) as decimal)/cast(count(ic.InstallationTeamID) as decimal) * 100 as decimal(8,2)) as ActionsCompletedPercentage "
                    + " from ops.InstallationScheduleCurrent ic "
                    + " inner "
                    + " join ops.InstallationTeam it on it.InstallationTeamID = ic.Installationteamid "
                    + " where IsCurrent = 1 "
                    + " group by InstallationTeamName, ic.InstallationTeamID order by installationTeamName").ToList();

                List<InstallationCounts> lstCounts = new List<InstallationCounts>();
                //we have the info, we can generate the emails here and send it on.

                generateExcelFileData.Data = myResults;
                generateExcelFileData.fileName = "InstallationsReport.xlsx";
                generateExcelFileData.includePicture = false;
                generateExcelFileData.saveAsAttachment = true;
                generateExcelFileData.Subject = "Installation Actions";

                helper.GenerateExcelFile(generateExcelFileData);





                //now we have the full report, we can create the lesser report as well
                return StatusCode(200, myResults);
            }

        }



        [Route("SendInstallationsPerManager")]
        [HttpPost]
        public ActionResult<object> SendInstallationsPerManager(string emailToSendTo)
        {
            HelperController helper = new HelperController();
            HelperController.GenerateExcelFileData generateExcelFileData = new HelperController.GenerateExcelFileData();
            generateExcelFileData.emailToSendTo = emailToSendTo;

            List<string> managerEmails = new List<string>();

            using (var context = new NovaDBContext())
            {
                var results = context.TeamManagers.Select(x => x.ApplicationUserId).Distinct().ToList();
                managerEmails = results;
            }
            foreach (string email in managerEmails)
            {
                using (var context = new NovaDBContext())
                {

                    var myResults =
                        context.InstallationCounts.FromSqlRaw("select  it.InstallationTeamName as installationTeamName,  count(ic.InstallationTeamID) as TotalActions "
                        + " , (Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 1   and icc.IsCurrent = 1) as ActionsCompleted "
                        + " ,(Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 0    and icc.IsCurrent = 1 ) as ActionsLeft "
                           + " ,(Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 1    and icc.IsCurrent = 1 and icc.FinishedDate >= '" + System.DateTime.Now.Date.ToString("yyyy/MM/dd") + "' ) as ActionsCompletedForTheDay "
                          + " ,Cast(Cast((Select count(InstallationTeamID) from ops.InstallationScheduleCurrent icc where icc.Installationteamid = ic.Installationteamid and icc.CampaignFinished = 1   and icc.IsCurrent = 1 ) as decimal)/cast(count(ic.InstallationTeamID) as decimal) * 100 as decimal(8,2)) as ActionsCompletedPercentage "
                        + " from ops.InstallationScheduleCurrent ic "
                        + " inner "
                        + " join ops.InstallationTeam it on it.InstallationTeamID = ic.Installationteamid "
                        + " inner "
                        + " join ops.TeamManager tm on tm.InstallationTeamID = ic.Installationteamid "
                        + " where IsCurrent = 1 "
                        + " and tm.applicationUserId = '" + email + "'"
                        + " group by InstallationTeamName, ic.InstallationTeamID order by installationTeamName").ToList();

                    List<InstallationCounts> lstCounts = new List<InstallationCounts>();
                    //we have the info, we can generate the emails here and send it on.

                    generateExcelFileData.Data = myResults;
                    generateExcelFileData.fileName = "InstallationsReport.xlsx";
                    generateExcelFileData.includePicture = false;
                    generateExcelFileData.saveAsAttachment = true;
                    generateExcelFileData.emailToSendTo = email;
                    generateExcelFileData.Subject = "Installation Actions";
                    helper.GenerateExcelFile(generateExcelFileData);

                    //now we have the full report, we can create the lesser report as well

                }
            }

            return StatusCode(200, null);
            //Querying with LINQ to Entities 


        }


        [Route("GetCapexMovement")]
        [HttpPost]
        public ActionResult<object> GetCapexMovement(getCapexMovementSearch search)
        {
            HelperController helper = new HelperController();
            HelperController.GenerateExcelFileData generateExcelFileData = new HelperController.GenerateExcelFileData();
            generateExcelFileData.emailToSendTo = search.emailToSendTo;

            List<string> managerEmails = new List<string>();

            using (var context = new NovaDBContext())
            {

                var myResults =
                    context.InventoryItemTransactions.Include(x => x.FromStore).Where(x => x.FromStore.StoreId == x.FromStoreId)
                    .Include(x => x.ToStore).ThenInclude(y => y.Region).ThenInclude(z => z.Chain)
                    .Where(x => x.ToStore.StoreId == x.ToStoreId)
                    .Include(x => x.FromInstallationTeam).Where(x => x.FromInstallationTeam.InstallationTeamId == x.FromVanId)
                    .Include(x => x.ToInstallationTeam).Where(x => x.ToInstallationTeam.InstallationTeamId == x.ToVanId)
                    .Include(x => x.FromWarehouse).Where(x => x.FromWarehouse.WarehouseId == x.FromWarehouseId)
                    .Include(x => x.ToWarehouse).Where(x => x.ToWarehouse.WarehouseId == x.ToWarehouseId)
                    .Include(x => x.MasterItemInvenoryItem).ThenInclude(x => x.MasterItem)
                    .Include(x => x.ToStore).ThenInclude(x => x.InstallationTeam)
                    .Where(x => x.CreationDate.Date >= search.fromDate.Date)
                    .Select(x => new { Barcode = x.Barcode, fromWarehoue = x.FromWarehouse.WarehouseName, FromTeam = x.FromInstallationTeam.InstallationTeamName, FromStore = x.FromStore.StoreName, towarehouse = x.ToWarehouse.WarehouseName, toTeam = x.ToInstallationTeam.InstallationTeamName, ToStore = x.ToStore.StoreName, MasterItemName = x.MasterItem.MasterItemName, MovementDate = x.CreationDate.ToShortDateString(), Creator = x.CreatedBy, chainName = x.ToStore.Region.Chain.ChainName, x.ToStore.InstallationTeam.InstallationTeamName })
                    .Distinct()
                    .ToList();

                List<InstallationCounts> lstCounts = new List<InstallationCounts>();
                //we have the info, we can generate the emails here and send it on.

                generateExcelFileData.Data = myResults;
                generateExcelFileData.fileName = "InventoryItemMovements.xlsx";
                generateExcelFileData.includePicture = false;
                generateExcelFileData.saveAsAttachment = true;
                generateExcelFileData.emailToSendTo = search.emailToSendTo;
                generateExcelFileData.Subject = "Inventory Item Movements";
                return myResults;
                // helper.GenerateExcelFile(generateExcelFileData);

                //now we have the full report, we can create the lesser report as well

            }

            return StatusCode(200, null);
            //Querying with LINQ to Entities 


        }


        [Authorize(Roles = "Admin,NovaOpsUser")]
        [Route("UpdateImageURLS")]
        [HttpPost]

        public async Task<object> UpdateImageURLS()
        {
            string host = "pcaimages.primeinstore.co.za";
            //string host = HttpContext.Request.Host.Value;
            host = "https://" + host;
            host = host + "/api";
            host = host + "/PhotoHanler";
            host = host + "/GetImages";
            host = host + "/?imageURL=";
            using (var context = new NovaDBContext())
            {
                //List<InstallationScheduleCurrent> lstCurrentInstallations = context.InstallationScheduleCurrent.Where(x => x.IsCurrent == true && x.imageURL == "").ToList();
                List<InstallationScheduleCurrent> lstCurrentInstallations = context.InstallationScheduleCurrent.Where(x => x.imageURL == "" && x.CampaignFinished == true).ToList();
                Parallel.ForEach(lstCurrentInstallations, new ParallelOptions { MaxDegreeOfParallelism = 36 }, ins =>
                {
                    if (Directory.Exists(Path.Combine(pathForImages, ins.JobNumber)))
                    {
                        string[] fileEntries = Directory.GetFiles(Path.Combine(pathForImages, ins.JobNumber), "*" + ins.InstallationScheduleCurrentID.ToString() + "*");
                        string pathForPicture = "";
                        foreach (string file in fileEntries)
                        {
                            if (file.Contains(ins.InstallationScheduleCurrentID.ToString().ToLower()))
                            {
                                pathForPicture = "";
                                //this must be it
                                pathForPicture = host + ins.JobNumber + @"\";
                                pathForPicture = host + file.Replace(pathForImages, "");
                                ins.imageURL = pathForPicture;

                                context.InstallationScheduleCurrent.Update(ins);

                                //we need to update here, remove till testing time.
                                //context.InstallationScheduleCurrent.Update(ins);

                            }
                        }
                    }


                });

                context.SaveChanges();
            }
            return null;
        }


        [Route("GetStorePayments")]
        [HttpPost]
        public ActionResult<object> GetStorePayments(string emailToSendTo)
        {
            HelperController helper = new HelperController();
            HelperController.GenerateExcelFileData generateExcelFileData = new HelperController.GenerateExcelFileData();
            generateExcelFileData.emailToSendTo = emailToSendTo;

            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                context.Database.SetCommandTimeout(900);
                //we need to think carefully here.
                var myResults = (from installationScheduleArchived in context.InstallationScheduleArchived
                                 join contract in context.Contract on installationScheduleArchived.JobNumber equals contract.ContractNumber
                                 join burst in context.Bursts on contract.ContractId equals burst.ContractID
                                 join burstCategory in context.BurstCategories on burst.BurstId equals burstCategory.BurstId
                                 join storeCategory in context.Categories1 on burstCategory.CategoryId equals storeCategory.CategoryId
                                 join irCode in context.IRCodes on installationScheduleArchived.IRCodeID equals irCode.IRCodeID
                                 join storePaymentSelect in context.StoreSelectedForPayments on installationScheduleArchived.StoId equals storePaymentSelect.StoreId
                                 join paymentClassification in context.PaymentClassifications on storePaymentSelect.PaymentClassificationId equals paymentClassification.Id
                                 join mediaRate in context.MediaRates on paymentClassification.Id equals mediaRate.PaymentClassificationId
                                 join typeOfPayment in context.TypeOfPayments on paymentClassification.TypeOfPaymentId equals typeOfPayment.Id
                                 // join storeCategoryTwo in context.Categories1 on installationScheduleArchived.CategoryName equals storeCategoryTwo.CategoryName
                                 //where contract.ContractNumber == "CS004580"
                                 where installationScheduleArchived.Store == "Harbour View Superspar"
                                 select new
                                 {
                                     Schedule = installationScheduleArchived,
                                     Contracts = contract,
                                     StoreCategory = storeCategory,
                                     BurstCategory = burstCategory,
                                     Burst = burst,
                                     IRCode = irCode,
                                     PaymentClassification = paymentClassification,
                                     MediaRate = mediaRate,
                                     TypeOfPayment = typeOfPayment
                                 }).Where(x => x.StoreCategory.CategoryName == x.Schedule.CategoryName && x.BurstCategory.CategoryId == x.StoreCategory.CategoryId && x.Schedule.Chain == x.Burst.ChainName && x.MediaRate.MediaId == x.Burst.MediaId && (x.Schedule.Status == "Install" || x.Schedule.Status == "Running")).ToList();

                var ProperResulst = myResults.Select(x => new { Burstid = x.Burst.BurstId, InstallationScheduleId = x.Schedule.InstallationScheduleCurrentID, ContractNumber = x.Contracts.ContractNumber, Category = x.StoreCategory.CategoryName, ScheduleCategory = x.Schedule.CategoryName, Store = x.Schedule.Store, Region = x.Schedule.Region, OtherCat = x.BurstCategory.CategoryId, Chain = x.Schedule.Chain, Status = x.Schedule.Status, IrCode = x.IRCode.IRCodeName, FirstWeek = x.Burst.FirstWeek, ScheduleWeek = Convert.ToDateTime(x.Schedule.ForDate), PaymentClassification = x.PaymentClassification.PaymentClassificationName, TypeOfPayment = x.TypeOfPayment.Type, MediaRate = x.MediaRate.MediaRate1, EffectiveDate = x.MediaRate.EffectiveDate }).OrderBy(x => x.ContractNumber).ToList();


                //how will we get the top effective date out of the data
                //we would now need to do more work I suppose.

                foreach (var item in ProperResulst)
                {
                    //get the latest effective date
                }
                List<InstallationCounts> lstCounts = new List<InstallationCounts>();
                //we have the info, we can generate the emails here and send it on.

                generateExcelFileData.Data = ProperResulst;
                generateExcelFileData.fileName = "StorePayments.xlsx";
                generateExcelFileData.includePicture = false;
                generateExcelFileData.saveAsAttachment = true;
                generateExcelFileData.Subject = "Store Payments";

                helper.GenerateExcelFile(generateExcelFileData);





                //now we have the full report, we can create the lesser report as well
                return StatusCode(200, myResults);
            }

        }


        [Route("GetInstallationScheduleCurrent")]
        [HttpPost]
        public ActionResult<object> GetInstallationScheduleCurrent(string emailToSendTo)
        {
            HelperController helper = new HelperController();
            HelperController.GenerateExcelFileData generateExcelFileData = new HelperController.GenerateExcelFileData();
            generateExcelFileData.emailToSendTo = emailToSendTo;

            using (var context = new NovaDBContext())
            {
                var myResults = context.InstallationScheduleCurrent
                .Include(k => k.SelectedIRCode)
                .Include(m => m.InstallationTeam)
                .Where(r => r.ForDate == GetPreviousDate().ToString("MMM dd, yyyy"))
                .Select(r => new
                {
                    JobNumber = r.JobNumber,
                    SpecialInstructions = r.SpecialInstructions,
                    Store = r.Store,
                    Region = r.Region,
                    Product = r.Product,
                    MediaType = r.MediaType,
                    LastModified = r.LastModified,
                    Chain = r.Chain,
                    Status = r.Status,
                    ForDate = r.ForDate,
                    InstallationDay = r.InstallationDay,
                    IRCodeName = r.SelectedIRCode.IRCodeName,
                    IRCodeComment = r.IRCodeComment,
                    InstallationTeamName = r.InstallationTeam.InstallationTeamName
                })
                .ToList();

                DataTable data = JsonConvert.DeserializeObject<DataTable>(JsonConvert.SerializeObject(myResults));

                generateExcelFileData.Data = data;
                generateExcelFileData.fileName = "InstallationsReport.xlsx";
                generateExcelFileData.includePicture = false;
                generateExcelFileData.saveAsAttachment = true;
                generateExcelFileData.Subject = "Installation Actions";

                helper.GenerateExcelFile(generateExcelFileData);

                return StatusCode(200, data);
            }
        }

        private static DateTime GetPreviousDate()
        {
            DayOfWeek desiredDay = DayOfWeek.Monday;
            int offsetAmount = (int)desiredDay - (int)DateTime.Now.DayOfWeek;
            DateTime date = DateTime.Now.AddDays(-7 + offsetAmount);

            return date;
        }


        public class StrikeRateMasterDetails
        {

            public StrikeRateMasterDetails()
            {
                //if(startDate == null)
                //{
                //    startDate = new DateTime(1999, 1, 1, 0, 0, 0);
                //}

                //if ((endDate == null) || (endDate.Value.Year == 1))
                //{
                //    endDate = new DateTime(2100, 1, 1, 0, 0, 0);
                //}
                store = "";
                chain = "";
                includePictureURL = true;
                includePictures = true;

                irCodesSelected = new List<string>();
                contractNumbersSelected = new List<string>();
                regionsSelected = new List<string>();
                storesSelected = new List<string>();
                chainsSelected = new List<string>();
                mediaSelected = new List<string>();
            }

            public string contractNumber { get; set; }
            public string team { get; set; }

            public DateTime? startDate { get; set; }

            public DateTime? endDate { get; set; }

            public bool includePictures { get; set; }

            public bool includePictureURL { get; set; }

            public List<IRCodes> irCodes { get; set; }

            public string reportName { get; set; }

            public string chain { get; set; }

            public string store { get; set; }

            public List<string> irCodesSelected { get; set; }

            public List<string> contractNumbersSelected { get; set; }

            public List<string> storesSelected { get; set; }

            public List<string> regionsSelected { get; set; }

            public List<string> chainsSelected { get; set; }

            public List<string> mediaSelected { get; set; }

            public List<string> actionSelected { get; set; }

            public List<string> categorySelected { get; set; }

        }


        public class StrikeRateReport
        {
            public string Name { get; set; }
            public string Route { get; set; }
            public string Type { get; set; }

            public string ContractNumber { get; set; }
            public int NoOfStoresSelected { get; set; }
            public int NoOfAddsUp { get; set; }
            public int NoOfNonInstallations { get; set; }

            public decimal OverallCompliance { get; set; }

        }

        public class BulkStrikeRateReports
        {
            public ImportedInstallationActions ImportedInstallationActions { get; set; }
            public IRCodes IRCodeS { get; set; }
        }

        public class BulkMasterDetails
        {
            public string ContractNumber { get; set; }
            public string Team { get; set; }

            public IRCodes IRCodeS { get; set; }
        }

        public class BulkMasterDetailsNoIrCode
        {
            public string ContractNumber { get; set; }
            public string Team { get; set; }


        }


        public class InstallationActionPictureReport
        {
            public string actionId { get; set; }
            public string Region { get; set; }
            public string Chain { get; set; }
            public string Store { get; set; }
            public string Contract { get; set; }

            public string Team { get; set; }
            public string Category { get; set; }
            public string Product { get; set; }
            public string MediaType { get; set; }
            public string Action { get; set; }


            public string IRCode { get; set; }

            public int IRCodeID { get; set; }
            public string IRCodeComment { get; set; }
            public DateTime ScheduleDate { get; set; }

            //public IRCodes SelectedIRCode { get; set; }

            public DateTime DateOpened { get; set; }

            public DateTime DateClosed { get; set; }

            public string ImageURL { get; set; }




            public FileContentResult Picture { get; set; }


        }


        public class MerchandisingReportResults
        {
            public string actionId { get; set; }
            public string Region { get; set; }
            public string Chain { get; set; }
            public string Store { get; set; }
            public string Contract { get; set; }

            public string Team { get; set; }
            public string Category { get; set; }
            public string Product { get; set; }
            public string MediaType { get; set; }
            public DateTime ScheduleDate { get; set; }
            //public IRCodes SelectedIRCode { get; set; }
            public DateTime DateOpened { get; set; }
            public DateTime DateClosed { get; set; }

            public string isDispo { get; set; }
            public string linesNeat { get; set; }
            public string pricedCorrect { get; set; }
            public string stockCounts { get; set; }
            public string stockRoomStock { get; set; }

            public string stockAged { get; set; }
            public string ImageURL { get; set; }
            public FileContentResult Picture { get; set; }

            public string Action { get; set; }
            public string IRCode { get; set; }
            public string IRCodeComment { get; set; }
        }


        public class MerchandisingReportResultsDynamic
        {
            public string actionId { get; set; }
            public string Region { get; set; }
            public string Chain { get; set; }
            public string Store { get; set; }
            public string Contract { get; set; }

            public string Team { get; set; }
            public string Category { get; set; }
            public string Product { get; set; }
            public string MediaType { get; set; }
            public DateTime ScheduleDate { get; set; }
            //public IRCodes SelectedIRCode { get; set; }
            public DateTime DateOpened { get; set; }
            public DateTime DateClosed { get; set; }

            public string isDispo { get; set; }
            public string linesNeat { get; set; }
            public string pricedCorrect { get; set; }
            public string stockCounts { get; set; }
            public string stockRoomStock { get; set; }

            public string stockAged { get; set; }
            public string ImageURL { get; set; }
            public FileContentResult Picture { get; set; }

            public string Action { get; set; }
            public string IRCode { get; set; }
            public string IRCodeComment { get; set; }

            List<string> Questions { get; set; }
            List<string> Answers { get; set; }

            public List<QuestionAndAnswerForReport> AnswerForReports;
        }

        public class QuestionAndAnswerForReport
        {
            public string Question { get; set; }
            public string Answer { get; set; }
            public string Comment { get; set; }
        }

        public class DetailsForPictureReport
        {
            public string campaignNumber { get; set; }
            public string store { get; set; }
            public string region { get; set; }
            public string chain { get; set; }

        }

        public class getCapexMovementSearch
        {
            getCapexMovementSearch()
            {
                fromDate = System.DateTime.Now;
            }
            public DateTime fromDate { get; set; }
            public string emailToSendTo { get; set; }
        }

    }
}
