﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddCategoryNameToInventoryItems : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "categoryName",
                schema: "Ops",
                table: "MasterItemInventoryItem",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "categoryName",
                schema: "Ops",
                table: "MasterItemInventoryItem");
        }
    }
}
