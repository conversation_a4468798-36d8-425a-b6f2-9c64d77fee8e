﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class InitialTestToBeEmptyFinalVersionFinal : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
               name: "InvetoryItemsWithMovement",
               columns: table => new
               {
                   InstallationTeamName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   MasterItemName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   id = table.Column<int>(type: "int", nullable: false),
                   masterItemTypeName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   qtyAtWarehouse = table.Column<int>(type: "int", nullable: false),
                   qtyBeingMoved = table.Column<int>(type: "int", nullable: false),
                   qtyOnHand = table.Column<int>(type: "int", nullable: false),
                   storeName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   warehouseName = table.Column<string>(type: "nvarchar(max)", nullable: true)
               },
               constraints: table =>
               {
               });
            migrationBuilder.DropTable(
                name: "InvetoryItemsWithMovement");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "InvetoryItemsWithMovement",
                columns: table => new
                {
                    InstallationTeamName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MasterItemName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    id = table.Column<int>(type: "int", nullable: false),
                    masterItemTypeName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    qtyAtWarehouse = table.Column<int>(type: "int", nullable: false),
                    qtyBeingMoved = table.Column<int>(type: "int", nullable: false),
                    qtyOnHand = table.Column<int>(type: "int", nullable: false),
                    storeName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    warehouseName = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                });
        }
    }
}
