﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class MigratingBeforeImportingMM : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.CreateTable(
            //    name: "InstallationCounts",
            //    columns: table => new
            //    {
            //        installationTeamName = table.Column<string>(type: "nvarchar(max)", nullable: true),
            //        ActionsCompleted = table.Column<int>(type: "int", nullable: false),
            //        ActionsCompletedForTheDay = table.Column<int>(type: "int", nullable: false),
            //        ActionsLeft = table.Column<int>(type: "int", nullable: false),
            //        TotalActions = table.Column<int>(type: "int", nullable: false),
            //        ActionsCompletedPercentage = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
            //    },
            //    constraints: table =>
            //    {
            //    });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DropTable(
            //    name: "InstallationCounts");
        }
    }
}
