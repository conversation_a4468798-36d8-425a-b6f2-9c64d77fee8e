﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddMediaIdNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Questions_Media_MediaId",
                schema: "Ops",
                table: "Questions");

            migrationBuilder.AlterColumn<int>(
                name: "MediaId",
                schema: "Ops",
                table: "Questions",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddForeignKey(
                name: "FK_Questions_Media_MediaId",
                schema: "Ops",
                table: "Questions",
                column: "MediaId",
                principalSchema: "Media",
                principalTable: "Media",
                principalColumn: "MediaID",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Questions_Media_MediaId",
                schema: "Ops",
                table: "Questions");

            migrationBuilder.AlterColumn<int>(
                name: "MediaId",
                schema: "Ops",
                table: "Questions",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Questions_Media_MediaId",
                schema: "Ops",
                table: "Questions",
                column: "MediaId",
                principalSchema: "Media",
                principalTable: "Media",
                principalColumn: "MediaID",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
