﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models.Demonstrations;
using PhoenixAPI.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Controllers.Sales
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Authorize(Roles = "SalesSupport")]
    [Route("api/Sales/[controller]")]
    public class DemonstrationsController : ControllerBase
    {
        private readonly IDemonstrationsService _demonstrationsService;
        public DemonstrationsController(IDemonstrationsService demonstrationsService)
        {
            _demonstrationsService = demonstrationsService;
        }

        [Route("InteractionNotification")]
        [HttpPost]
        public async Task<object> InteractionNotification(DemonstrationRequest request)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                return await _demonstrationsService.InteractionNotification(request, email).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.InnerException.Message;
            }
        }

        [Route("CheckIfStoreListIsConfirmed")]
        [HttpPost]
        public async Task<object> CheckIfStoreListIsConfirmed(DemonstrationRequest request)
        {
            try
            {
                return await _demonstrationsService.CheckIfStoreListIsConfirmed(request).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.InnerException.Message;
            }
        }

        [Route("CheckIfBurstExist")]
        [HttpPost]
        public async Task<object> CheckIfBurstExist(DemonstrationRequest request)
        {
            try
            {
                return await _demonstrationsService.CheckIfBurstExist(request).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.InnerException.Message;
            }
        }

        [Route("CheckIfContractExist")]
        [HttpGet]
        public async Task<object> CheckIfContractExist(string contractNumber)
        {
            try
            {
                return await _demonstrationsService.CheckIfContractExist(contractNumber).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        [Route("GetChains")]
        [HttpGet]
        public async Task<object> GetChains()
        {
            try
            {
                return await _demonstrationsService.GetChains().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }
    }
}
