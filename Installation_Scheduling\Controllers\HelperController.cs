﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PhoenixAPI.Models;
using PhoenixAPI.Entities;

using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Hosting;
using System.Net.Http.Headers;
using System.Data;
using System.Data.OleDb;
using ClosedXML.Excel;
using System.Net;
using MetadataExtractor;
using MetadataExtractor.Formats.Exif;
using System.Net.Mail;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Reflection;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Drawing;
using System.Text;
using System.Drawing.Imaging;
using ClosedXML.Excel.Drawings;

namespace PhoenixAPI.Controllers
{

    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "Admin")]
    public class HelperController : ControllerBase
    {

        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IConfiguration _configuration;

        private static readonly SmtpClient smtpClient = new SmtpClient();
        private static readonly MailServer mailServer = new MailServer();
        private static readonly string SummaryTemplatePDF =  @"\\************\API Resources\Resources\EmailTemplates\SummaryPDFTemplate.html";
        private static readonly Attachment FooterAttachment = new Attachment( @"\\************\API Resources\Resources\Images\Footer.PNG");
        private static string MailText;
        private static string contentID;
        private static string pathForImages = @"\\************\Images";
        private static string pathForExcelFiles = @"\\************\Excel Files";
        private static string pathForPDFSaving = @"\\************\API Resources\Resources\PDF Files Generated\";
        private static string pathForFooter = @"\\************\API Resources\Resources\Images\FooterCropped.PNG";
        private static readonly string StrikeRateReportPDFTemplate = @"\\************\API Resources\Resources\EmailTemplates\StrikeRatePDFAttachment.html";
        private ExceptionController ExceptionController = new ExceptionController();
        public HelperController()
       {
            GetMailServerDetails();
            InitializeSMTP();
            contentID = "footerImage";
            FooterAttachment.ContentId = contentID;
        }

        public class FIleUploadAPI
        {
         
            public string campaignNumber { get; set; }
        }

        // POST: api/Image
       

        [Route("UploadImageTest")]
        [HttpPost, DisableRequestSizeLimit]
        public async Task<object> Upload()
        {
            try
            {

                var file = Request.Form.Files[0];
                var folderName = Path.Combine("Resources", "Images");
                var pathToSave = Path.Combine(System.IO.Directory.GetCurrentDirectory(), folderName);

                //ok change it here a bit.
                //ok, lets get campaign from this
                string campaignName = file.FileName.Substring(0, 8);
               // file.FileName = file.FileName.Replace(":", "").Replace("", "");

                //check if the folder exists
                var fileFolderPathWithContractNumber = Path.Combine(pathForImages, campaignName);
                System.IO.Directory.CreateDirectory(fileFolderPathWithContractNumber);

                if (file.Length > 0)
                {
                    var fileName = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"').Replace(":", "").Replace(campaignName, "");
                    var fullPath = Path.Combine(fileFolderPathWithContractNumber, fileName);
                    var dbPath = Path.Combine(fileFolderPathWithContractNumber, fileName);

                    using (var stream = new FileStream(fullPath, FileMode.Create))
                    {
                       await  file.CopyToAsync(stream);
                        return "";
                       // file.CopyTo(stream);
                    }

                  return "";
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                 ExceptionController.HandleException(ex);
                 return StatusCode(500, $"Internal server error: {ex}");
            }
        }

        [Route("uploadBaseFile")]
        [HttpPost, DisableRequestSizeLimit]
        public int uploadBaseFile(baseFilesToUpload baseFiles)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(baseFiles.baseImage.Replace("data:image/png;base64,","").Replace("data:image/jpeg;base64,","").Replace("data:text/html;base64,", ""));
                //byte[] bytes = System.Text.Encoding.Unicode.GetBytes(baseFiles.baseImage);



                Image image;

                MemoryStream imageStream =  new MemoryStream(bytes);
                image = Image.FromStream(imageStream);

                
                var folderName = Path.Combine("Resources", "Images");
                var pathToSave = Path.Combine(System.IO.Directory.GetCurrentDirectory(), folderName);
                string contractNumber = "";
                using (var context = new NovaDBContext())
                {
                     contractNumber = context.InstallationScheduleCurrent.Where(x => x.InstallationScheduleCurrentID == new Guid(baseFiles.actionID)).Select(x => x.JobNumber).FirstOrDefault().ToString();
                }
                    //ok change it here a bit.
                    //ok, lets get campaign from this
                    string campaignName = contractNumber;
                // file.FileName = file.FileName.Replace(":", "").Replace("", "");

                //check if the folder exists
                var fileFolderPathWithContractNumber = Path.Combine(pathForImages, campaignName);
                System.IO.Directory.CreateDirectory(fileFolderPathWithContractNumber);
                var fileName = new System.Guid(baseFiles.actionID).ToString();
                var fullPath = Path.Combine(fileFolderPathWithContractNumber, fileName);

                if(System.IO.File.Exists(fullPath + ".jpeg"))
                {
                    fullPath = fullPath + "-" + System.Guid.NewGuid();
                }
                image.Save(fullPath + ".jpeg", System.Drawing.Imaging.ImageFormat.Jpeg);

                imageStream.Close();
                return baseFiles.id;
            }
            catch(Exception ex)
            {
                //return StatusCode(500, $"Internal server error: {ex}");
                return 0 ;
            }
             
        }


    

        [Route("GetVisits")]
        [HttpGet]
        //gets the Store Visits for the User
        //here we will do better
        public IEnumerable<StoresWithInstallations> GetStoreVisits()
        {
            using (var context = new NovaDBContext())
            {



                var results = context.VMyMobilityExportWithDays

                    .ToList().OrderBy(x => x.Store);

                //lets do different
                List<StoresWithInstallations> storeNameDays = results.GroupBy(x => new { x.StoId, x.Store, installationDay = x.InstallationDay, x.Region, x.ForDate, x.Chain }).Select(y => new StoresWithInstallations() { storeId = y.Key.StoId, storeName = y.Key.Store, storeInstallDay = y.Key.installationDay, forDate = y.Key.ForDate, region = y.Key.Region, storeInstallations = null, chainName = y.Key.Chain }).OrderBy(z => z.storeName).ToList();

                //ok, we have distinct stores, lets build the stuff now

                foreach (StoresWithInstallations sday in storeNameDays)
                {
                    //lets get all the stuff here
                    List<VMyMobilityExportWithDays> lstVMyMobilityExport = results.Where(x => x.Store == sday.storeName).ToList();
                    sday.storeInstallations = new List<InstallationsForStore>();
                    sday.storeInstallations = lstVMyMobilityExport.Select(x => new InstallationsForStore() { categoryName = x.CategoryName, client = x.Client, dayOfCommencementDate = x.DayOfCommencementDate, dayOfTerminationDate = x.DayOfTerminationDate, jobNumber = x.JobNumber, mediaType = x.MediaType, product = x.Product, qtyToInstall = x.QtyToInstall, status = x.Status, installationInstructions = x.SpecialInstructions }).ToList();
                }

                var dayIndex = new List<string> { "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY" };

                //now we have all stores
                return storeNameDays.OrderBy(x => dayIndex.IndexOf(x.storeInstallDay.ToUpper())).ToList();
                //IEnumerable<StoresWithInstallations> listB = results.Select(a => new StoresWithInstallations()
                //{
                //    storeId = a.StoId,
                //    storeName = a.Store,
                //    region = a.Region,
                //    forDate = a.ForDate,
                //    storeInstallations =  new List<InstallationsForStore>
                //    {

                //        results.GroupBy(x => new { x.Store }).Select(y => new StoresWithInstallations() { FirstWeek = y.Key., BrandName = y.Key.BrandName }).OrderBy(z => z.BrandName).ToList()
                ////results.GroupBy(x => x.DayOfCommencementDate).Where(x => x.Key.)
                //    }
                //}).ToList();

            }
        }

        [Route("GetVisitsByStores/{storeName}")]
        [HttpGet]
        //gets the Store Visits for the User

        public IEnumerable<VMyMobilityExport> GetStoreVisitsByStore(string storeName)
        {
            using (var context = new NovaDBContext())
            {
                return context.VMyMobilityExport
                    .Where(x => x.Store == storeName)
                    .ToList().OrderBy(x => x.Store);
            }
        }


        [Route("UploadFileWithData")]
        [HttpPost, DisableRequestSizeLimit]
        public IActionResult UploadFileAndOtherData(string apiToCall, [FromForm] IFormFileCollection files )
        {
            try
            {
                var folderName = Path.Combine("Resources", "Excel Files");
                var pathToSave = Path.Combine(System.IO.Directory.GetCurrentDirectory(), folderName);
                if (files.Count > 0)
                {
                    foreach(FormFile file in files)
                    {
                        var fileName = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"').Replace(":", "");
                        var fullPath = Path.Combine(pathForExcelFiles, fileName);
                        var dbPath = Path.Combine(pathForExcelFiles, fileName);
                        using (var stream = new FileStream(fullPath, FileMode.Create))
                        {
                            file.CopyTo(stream);
                        }
                        //if there is an api to call, lets do that
                        if(apiToCall != "")
                        {
                            //over here we will determine which api to call
                            if(apiToCall == "StrikeRateReport")
                            {
                                GenerateStrikeRateReport(fullPath);
                            }
                        }
                    }
                    return Ok("Good");
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                 ExceptionController.HandleException(ex);
                return StatusCode(500, $"Internal server error: {ex}");
            }
        }


        [Route("UploadFileWithDataTest")]
        [HttpPost, DisableRequestSizeLimit]
        public async Task UploadFileWithDataTest(string apiToCall)
        {
            try
            {
                var file = Request.Form.Files[0];
                var folderName = Path.Combine("Resources", "Excel Files");
                var pathToSave = Path.Combine(System.IO.Directory.GetCurrentDirectory(), folderName);
                
                        var fileName = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName.Trim('"').Replace(":", "");
                        var fullPath = Path.Combine(pathForExcelFiles, fileName);
                        var dbPath = Path.Combine(pathForExcelFiles, fileName);
                        using (var stream = new FileStream(fullPath, FileMode.Create))
                        {
                            file.CopyTo(stream);
                        }
                        //if there is an api to call, lets do that
                        if (apiToCall != "")
                        {
                            //over here we will determine which api to call
                            if (apiToCall == "StrikeRateReport")
                            {
                              await GenerateStrikeRateReport(fullPath);
                            }
                        }

               


            }
            catch (Exception ex)
            {
                await ExceptionController.HandleException(ex);
            }
        }

        public async Task  GenerateStrikeRateReport(string filePathForReport)
        {
            //we definately need to rework this properly, it takes too long.

            ReportsController reportsController = new ReportsController();
            List<ImportedInstallationActions> lstImportedInstallationActions = new List<ImportedInstallationActions>();
            //now we need to actually read the file, and then write it to the database
            //Started reading the Excel file.  
            DataTable dt = new DataTable();
            using (XLWorkbook workbook = new XLWorkbook(filePathForReport))
            {
                IXLWorksheet worksheet = workbook.Worksheet(1);
                bool FirstRow = true;
                //Range for reading the cells based on the last cell used.  
                string readRange = "1:1";
                foreach (IXLRow row in worksheet.RowsUsed())
                {
                    //If Reading the First Row (used) then add them as column name  
                    if (FirstRow)
                    {
                        //Checking the Last cellused for column generation in datatable  
                        readRange = string.Format("{0}:{1}", 1, row.LastCellUsed().Address.ColumnNumber);
                        foreach (IXLCell cell in row.Cells(readRange))
                        {
                            dt.Columns.Add(cell.Value.ToString());
                        }
                        FirstRow = false;
                    }
                    else
                    {
                        //Adding a Row in datatable  
                        dt.Rows.Add();
                        int cellIndex = 0;
                        //Updating the values of datatable  
                        foreach (IXLCell cell in row.Cells(readRange))
                        {
                            dt.Rows[dt.Rows.Count - 1][cellIndex] = cell.Value.ToString();
                            cellIndex++;
                        }
                    }
                }
            }
            int mycounter = 0;
            foreach (DataRow dr in dt.Rows)
            {
                //here we need to conver the rows
                
                  
                    ImportedInstallationActions importedInstallationActions = new ImportedInstallationActions();
                    try
                    {
                    //lets try to add it here
                        
                        importedInstallationActions.Action = dr["Action"].ToString();
                        importedInstallationActions.Category = dr["Product Category"].ToString();
                        importedInstallationActions.Chain = dr["Chain (Store) (Account)"].ToString();
                        importedInstallationActions.ContractNumber = dr["Contract Number"].ToString();
                        importedInstallationActions.DateCompleted = Convert.ToDateTime(dr["Date Completed"].ToString());
                        bool haspicture = dr["HasPicture"].ToString().ToLower() == "yes";
                        importedInstallationActions.HasPicture = haspicture;
                        importedInstallationActions.IRCode = dr["IR"].ToString();
                        importedInstallationActions.IRNotes = dr["IR Notes"].ToString();
                        importedInstallationActions.MediaType = dr["Media Type"].ToString();
                        importedInstallationActions.MyMobilityCategory = dr["MyMobility Product"].ToString();
                        importedInstallationActions.PictureURL = dr["Picture URL (Link)"].ToString();
                        importedInstallationActions.Region = dr["Region (Store) (Account)"].ToString();
                        importedInstallationActions.ScheduleDate = Convert.ToDateTime(dr["Schedule Date"].ToString());
                        importedInstallationActions.Store = dr["Store"].ToString();
                        importedInstallationActions.Owner = dr["Owner"].ToString();
                        importedInstallationActions.strikeRateReportGenerated = false;


                         lstImportedInstallationActions.Add(importedInstallationActions);

                    }
                    catch (Exception ex)
                    {
                    await ExceptionController.HandleException(ex);
                }
                
               
            }

           
            //ok, lets get everything with same date
            List<DateTime> lstScheduleDates = new List<DateTime>();
            lstScheduleDates = lstImportedInstallationActions.Select(x => x.ScheduleDate).Distinct().ToList();

            //ok, now get everything form database with same dates
            List<ImportedInstallationActions> lstWithDates = new List<ImportedInstallationActions>();
            


            using (var db = new NovaDBContext())
            {
                foreach(DateTime dateTime in lstScheduleDates)
                {
                    lstWithDates.AddRange(db.ImportedInstallationActions.Where(x => x.ScheduleDate == dateTime).Select(x => new ImportedInstallationActions { Action = x.Action, Category = x.Category, Chain = x.Chain, ContractNumber = x.ContractNumber, DateCompleted = x.DateCompleted, HasPicture = x.HasPicture, IRCode = x.IRCode, IRNotes = x.IRNotes, MediaType = x.MediaType, MyMobilityCategory = x.MyMobilityCategory, Owner = x.Owner, PictureURL = x.PictureURL, Region = x.Region, ScheduleDate = x.ScheduleDate, Store = x.Store }).ToList());
                }
               

               
            }

            var MS = lstImportedInstallationActions.Select(x => new  { x.Action,  x.Category ,  x.Chain,  x.ContractNumber,  x.DateCompleted,  x.HasPicture,  x.IRCode,  x.IRNotes,  x.MediaType,  x.MyMobilityCategory,  x.Owner,  x.PictureURL,  x.Region,  x.ScheduleDate,  x.Store })
                    .Except(lstWithDates.Select(x => new  {  x.Action,  x.Category ,  x.Chain,  x.ContractNumber,  x.DateCompleted,  x.HasPicture,  x.IRCode,  x.IRNotes,  x.MediaType,  x.MyMobilityCategory,  x.Owner,  x.PictureURL,  x.Region,  x.ScheduleDate,  x.Store })).ToList();


            List<ImportedInstallationActions> lstAllToImport = MS.Select(x => new ImportedInstallationActions {Action = x.Action, Category = x.Category,Chain = x.Chain,ContractNumber = x.ContractNumber,DateCompleted = x.DateCompleted,HasPicture = x.HasPicture,IRCode =  x.IRCode,IRNotes = x.IRNotes,MediaType = x.MediaType,MyMobilityCategory = x.MyMobilityCategory,Owner = x.Owner, PictureURL = x.PictureURL,Region = x.Region,ScheduleDate = x.ScheduleDate,Store = x.Store , strikeRateReportGenerated = false}).ToList();
            using (var db = new NovaDBContext())
            {

                db.ImportedInstallationActions.AddRange(lstAllToImport);
                db.SaveChanges();
            }

            return;
            List<ReportsController.BulkStrikeRateReports> lstBulk = new List<ReportsController.BulkStrikeRateReports>();
            List<ReportsController.BulkMasterDetails> lstBulkMasterDetails = new List<ReportsController.BulkMasterDetails>();


            using (var db = new NovaDBContext())
            {
                //lets get everything here
                var query = from ImportedInstallationActions in db.Set<ImportedInstallationActions>().Where(x => x.strikeRateReportGenerated == false )
                            join IRCodeS in db.Set<IRCodes>()
                                on ImportedInstallationActions.IRCode equals IRCodeS.IRCodeName
                            select new ReportsController.BulkStrikeRateReports(){ ImportedInstallationActions = ImportedInstallationActions, IRCodeS = IRCodeS }
                                ;
                lstBulk = query.ToList();
                //we should have the list now in total

            }
            lstBulkMasterDetails = lstBulk.GroupBy(x => new { x.ImportedInstallationActions.ContractNumber, x.ImportedInstallationActions.Owner, x.IRCodeS })
                .Select(g => new ReportsController.BulkMasterDetails { ContractNumber = g.Key.ContractNumber, Team = g.Key.Owner, IRCodeS = g.Key.IRCodeS }).ToList();


            //ok, now we need to pass the full list, and do calcs this way.
           List<ReportsController.StrikeRateReport> lstStrikeRateReports = reportsController.GenerateBulkStrikeRateReports(lstBulkMasterDetails);


            //somewhere along the lines we need to create those pdf's and also send them via email around here
            //here we will start sending the pdf's
            //lets group them
            //List<ReportsController.StrikeRateMasterDetails> lstStrikeRateMasterDetails = new List<ReportsController.StrikeRateMasterDetails>();
            ////now select a list of strike rate master details from the imported actions
            //lstStrikeRateMasterDetails = lstAllToImport.GroupBy(x => new { x.ContractNumber, x.Owner })
            //    .Select(g => new ReportsController.StrikeRateMasterDetails { contractNumber = g.Key.ContractNumber, team = g.Key.Owner }).ToList();
          
            
            //ok, we should have a list now, now we can send the email attachements.

           

            string asb = "";
            Task.Run(() => generatePDFReportsStrikeRate(lstStrikeRateReports)).ConfigureAwait(false);
            //foreach (ReportsController.StrikeRateMasterDetails strikeRateMasterDetails in lstStrikeRateMasterDetails)
            //{
            //    try
            //    {
            //         Task.Run(() => reportsController.GenerateStrikeRateReport(strikeRateMasterDetails)).ConfigureAwait(false);
            //        //lstStrikeRateReports.Add(reportsController.GenerateStrikeRateReport(strikeRateMasterDetails));
            //    }
            //    catch(Exception ex)
            //    {

            //    }
               
            //}
            //lets time it here

            string myTime = "llll";
            //List<ReportsController.StrikeRateReport> lstStrikeRateReports = new List<ReportsController.StrikeRateReport>();
            return;
            //add a foreach here for all managers,
            using (var db = new NovaDBContext())
            {
                var blogs = db.InstallationTeam
                .FromSqlRaw("select ops.InstallationTeam.*,ops.TeamManager.ApplicationUserId,ops.TeamManager.TeamManagerId  from ops.InstallationTeam left outer join ops.TeamManager on ops.TeamManager.InstallationTeamId = ops.InstallationTeam.InstallationTeamID")
                .ToList();
                var returnResult = await db.TeamManagers.ToListAsync();

                List<string> teamManagers = new List<string>();
                teamManagers = returnResult.Select(x => x.ApplicationUserId).Distinct().ToList();

                foreach(string strManager in teamManagers)
                {
                    //lets find all teams for him
                    List<string> teams = new List<string>();
                    teams = returnResult.Where(x => x.ApplicationUserId == strManager).Select(x => x.InstallationTeam.InstallationTeamName).Distinct().ToList();
                    //ok, we have the teams, lets find all attachments and send email.
                    List<ReportsController.StrikeRateReport> reportsForManagers = new List<ReportsController.StrikeRateReport>();
                    foreach(string team in teams)
                    {
                        ReportsController.StrikeRateReport reportToAdd = new ReportsController.StrikeRateReport();
                        reportToAdd = lstStrikeRateReports.Where(x => x.Name.ToLower().Replace("gauteng", "gau") == team.ToLower()).SingleOrDefault();
                        if(reportToAdd != null)
                        {
                            reportsForManagers.Add(reportToAdd);
                        }
                        
                    }

                    //we should now have all the reports for the manager
                    //ok, now we have the reports, lets send them
                    List<PDFAttachmentDetails> lstPDFAttachments = new List<PDFAttachmentDetails>();
                    foreach (ReportsController.StrikeRateReport strikeRateReport in reportsForManagers)
                    {
                        //you need to now create the attachements.
                        PDFAttachmentDetails pDFAttachmentDetails = new PDFAttachmentDetails();
                        pDFAttachmentDetails.pdfName = strikeRateReport.ContractNumber + " for " + strikeRateReport.Name + ".pdf";
                        pDFAttachmentDetails.pdfFolder = "Strike Rate Reports";
                        pDFAttachmentDetails.pdfTemplate = @"\\************\API Resources\Resources\EmailTemplates\StrikeRatePDFAttachment.html";
                        List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                        replacementValues.Add(new ReplacementValues { StringToFind = "[[AddsUp]]", valueToReplaceWith = strikeRateReport.NoOfAddsUp.ToString() });
                        replacementValues.Add(new ReplacementValues { StringToFind = "[[NonInstallations]]", valueToReplaceWith = strikeRateReport.NoOfNonInstallations.ToString() });
                        replacementValues.Add(new ReplacementValues { StringToFind = "[[OverallCompliance]]", valueToReplaceWith = strikeRateReport.OverallCompliance.ToString() });
                        replacementValues.Add(new ReplacementValues { StringToFind = "[[StoresSelected]]", valueToReplaceWith = strikeRateReport.NoOfStoresSelected.ToString() });
                        replacementValues.Add(new ReplacementValues { StringToFind = "[[CampaignNumber]]", valueToReplaceWith = strikeRateReport.ContractNumber.ToString() });
                        replacementValues.Add(new ReplacementValues { StringToFind = "[[InformationSpan]]", valueToReplaceWith = "Please see the strike rate report for team " + strikeRateReport.Name + " for contract " + strikeRateReport.ContractNumber.ToString() });
                        pDFAttachmentDetails.replacementValuesPDF = replacementValues;
                        lstPDFAttachments.Add(pDFAttachmentDetails);
                    }
                    try
                    {
                        //now we can finally send the email.(Actually, we still need to do the managers, but for now lets send it to me).
                        EmailData emailData = new EmailData();
                        //emailData.carbonCopies.Add("<EMAIL>");
                        emailData.PDFAttachmentDetails = lstPDFAttachments;
                        emailData.subject = strManager;
                        emailData.recipient = "<EMAIL>";
                        await Task.Run(sendEmail(emailData));
                    }
                    catch
                    {

                    }
                    
                }


            }



            foreach(ImportedInstallationActions importedInstallationActions in lstImportedInstallationActions)
            {
                downloadAndStoreImages(importedInstallationActions);
            }
            //lets move this one to a later stage
           
            //List < CountReport > lstCountReport = result
            //   .GroupBy(x => new { x.Createdby, x.Firstweek })
            //      .Select(g => new CountReport { Creator = g.Key.Createdby, DateRange = g.Key.Firstweek, Amount = g.Count() }).ToList();

        }

        //ok, lets work with creating the pdf's here
        public async Task generatePDFReportsStrikeRate(List<ReportsController.StrikeRateReport> lstStrikeRateReports)
        {
            //List<ReportsController.StrikeRateReport> lstStrikeRateReports = new List<ReportsController.StrikeRateReport>();
            //ReportsController reportsController = new ReportsController();

            //foreach (ReportsController.StrikeRateMasterDetails strikeRateMasterDetails in lstStrikeRateMasterDetails)
            //{
            //    try
            //    {
            //        //reportsController.GenerateStrikeRateReport(strikeRateMasterDetails));
            //        lstStrikeRateReports.Add(reportsController.GenerateStrikeRateReport(strikeRateMasterDetails));
            //    }
            //    catch (Exception ex)
            //    {
            //        await ExceptionController.HandleException(ex);
            //    }
            //}
            ////when it is done
            //string asdf = "asdfsfd";

            using (var db = new NovaDBContext())
            {
                var blogs = db.InstallationTeam
                .FromSqlRaw("select ops.InstallationTeam.*,ops.TeamManager.ApplicationUserId,ops.TeamManager.TeamManagerId  from ops.InstallationTeam left outer join ops.TeamManager on ops.TeamManager.InstallationTeamId = ops.InstallationTeam.InstallationTeamID")
                .ToList();
                var returnResult = await db.TeamManagers.ToListAsync();

                List<string> teamManagers = new List<string>();
                teamManagers = returnResult.Select(x => x.ApplicationUserId).Distinct().ToList();

                foreach (string strManager in teamManagers)
                {
                    //lets find all teams for him
                    List<string> teams = new List<string>();
                    teams = returnResult.Where(x => x.ApplicationUserId == strManager).Select(x => x.InstallationTeam.InstallationTeamName).Distinct().ToList();
                    //ok, we have the teams, lets find all attachments and send email.
                    List<ReportsController.StrikeRateReport> reportsForManagers = new List<ReportsController.StrikeRateReport>();
                    foreach (string team in teams)
                    {
                       
                        List<ReportsController.StrikeRateReport> reportToAdd = new List<ReportsController.StrikeRateReport>();
                        try
                        {
                            reportToAdd = lstStrikeRateReports.Where(x => x.Name.ToLower().Replace("gauteng", "gau") == team.ToLower()).ToList();
                            if (reportToAdd != null)
                            {
                                reportsForManagers.AddRange(reportToAdd);
                            }
                        }
                        catch(Exception ex)
                        {
                            await ExceptionController.HandleException(ex);
                        }
                       

                    }

                    List<PDFAttachmentDetails> lstPDFAttachments = new List<PDFAttachmentDetails>();
                    try
                    {
                        string emailPathOfFiles = @"Please find your pdf's for the strike rate reports here : \\************\API Resources\Resources\PDF Files Generated\Strike Rate Reports" + "\\" + System.DateTime.Now.ToShortDateString().ToString().Replace("/", "-");



                        Parallel.ForEach(reportsForManagers, new ParallelOptions { MaxDegreeOfParallelism = 36 }, strikeRateReport =>
                        {

                            PDFAttachmentDetails pDFAttachmentDetails = new PDFAttachmentDetails();
                            pDFAttachmentDetails.pdfName = strikeRateReport.ContractNumber + " for " + strikeRateReport.Name + ".pdf";
                            pDFAttachmentDetails.pdfFolder = "Strike Rate Reports" + "\\" + System.DateTime.Now.ToShortDateString().ToString().Replace("/", "-") + "\\" + strikeRateReport.Name;
                            pDFAttachmentDetails.pdfTemplate = @"\\************\API Resources\Resources\EmailTemplates\StrikeRatePDFAttachment.html";
                            List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                            replacementValues.Add(new ReplacementValues { StringToFind = "[[AddsUp]]", valueToReplaceWith = strikeRateReport.NoOfAddsUp.ToString() });
                            replacementValues.Add(new ReplacementValues { StringToFind = "[[NonInstallations]]", valueToReplaceWith = strikeRateReport.NoOfNonInstallations.ToString() });
                            replacementValues.Add(new ReplacementValues { StringToFind = "[[OverallCompliance]]", valueToReplaceWith = strikeRateReport.OverallCompliance.ToString() });
                            replacementValues.Add(new ReplacementValues { StringToFind = "[[StoresSelected]]", valueToReplaceWith = strikeRateReport.NoOfStoresSelected.ToString() });
                            replacementValues.Add(new ReplacementValues { StringToFind = "[[CampaignNumber]]", valueToReplaceWith = strikeRateReport.ContractNumber.ToString() });
                            replacementValues.Add(new ReplacementValues { StringToFind = "[[InformationSpan]]", valueToReplaceWith = "Please see the strike rate report for team " + strikeRateReport.Name + " for contract " + strikeRateReport.ContractNumber.ToString() });
                            pDFAttachmentDetails.replacementValuesPDF = replacementValues;
                            lstPDFAttachments.Add(pDFAttachmentDetails);
                            generatePDFFile(pDFAttachmentDetails);
                        });


                        //foreach (ReportsController.StrikeRateReport strikeRateReport in reportsForManagers)
                        //{
                        //    //you need to now create the attachements.
                        //    PDFAttachmentDetails pDFAttachmentDetails = new PDFAttachmentDetails();
                        //    pDFAttachmentDetails.pdfName = strikeRateReport.ContractNumber + " for " + strikeRateReport.Name + ".pdf";
                        //    pDFAttachmentDetails.pdfFolder = "Strike Rate Reports" + "\\" + System.DateTime.Now.ToShortDateString().ToString().Replace("/","-") + "\\" + strikeRateReport.Name;
                        //    pDFAttachmentDetails.pdfTemplate = @"\\************\API Resources\Resources\EmailTemplates\StrikeRatePDFAttachment.html";
                        //    List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                        //    replacementValues.Add(new ReplacementValues { StringToFind = "[[AddsUp]]", valueToReplaceWith = strikeRateReport.NoOfAddsUp.ToString() });
                        //    replacementValues.Add(new ReplacementValues { StringToFind = "[[NonInstallations]]", valueToReplaceWith = strikeRateReport.NoOfNonInstallations.ToString() });
                        //    replacementValues.Add(new ReplacementValues { StringToFind = "[[OverallCompliance]]", valueToReplaceWith = strikeRateReport.OverallCompliance.ToString() });
                        //    replacementValues.Add(new ReplacementValues { StringToFind = "[[StoresSelected]]", valueToReplaceWith = strikeRateReport.NoOfStoresSelected.ToString() });
                        //    replacementValues.Add(new ReplacementValues { StringToFind = "[[CampaignNumber]]", valueToReplaceWith = strikeRateReport.ContractNumber.ToString() });
                        //    replacementValues.Add(new ReplacementValues { StringToFind = "[[InformationSpan]]", valueToReplaceWith = "Please see the strike rate report for team " + strikeRateReport.Name + " for contract " + strikeRateReport.ContractNumber.ToString() });
                        //    pDFAttachmentDetails.replacementValuesPDF = replacementValues;
                        //    lstPDFAttachments.Add(pDFAttachmentDetails);
                        //    generatePDFFile(pDFAttachmentDetails);
                        //}
                        try
                        {
                            ////now we can finally send the email.(Actually, we still need to do the managers, but for now lets send it to me).
                            //EmailData emailData = new EmailData();
                            //ReplacementValues replacementValues = new ReplacementValues();
                            //replacementValues.StringToFind = "[[FullText]]";
                            //replacementValues.valueToReplaceWith = emailPathOfFiles;
                            //////emailData.carbonCopies.Add("<EMAIL>");
                            ////emailData.PDFAttachmentDetails = lstPDFAttachments;
                            //emailData.subject = strManager + " " + emailPathOfFiles;
                            //emailData.recipient = "<EMAIL>";
                            //await Task.Run(sendEmail(emailData));
                        }
                        catch(Exception ex)
                        {
                            await ExceptionController.HandleException(ex);
                        }
                    }
                    catch(Exception ex)
                    {
                        await ExceptionController.HandleException(ex);
                    }
                    //we should now have all the reports for the manager
                    //ok, now we have the reports, lets send them
                   
                   

                }


            }

        }


        public bool isExistingImportedActiong(ImportedInstallationActions importedInstallationActions)
        {
            using (var db = new NovaDBContext())
            {
                var a = db.ImportedInstallationActions.Where(x => x.DateCompleted == importedInstallationActions.DateCompleted && x.ContractNumber == importedInstallationActions.ContractNumber && x.Store == importedInstallationActions.Store && x.MediaType == importedInstallationActions.MediaType && x.Category == importedInstallationActions.Category && x.MediaType == importedInstallationActions.MediaType ).Count();
                if(a > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
        }


        public async Task  downloadAndStoreImages(ImportedInstallationActions importedAction)
        {
            if (importedAction.PictureURL.ToString() != "")
            {
                //check if folder exists for contract, if now, create the folder
                var fileFolderPathWithContractNumber = Path.Combine(pathForImages, importedAction.ContractNumber);
                fileFolderPathWithContractNumber = Path.Combine(fileFolderPathWithContractNumber, importedAction.Region);
                fileFolderPathWithContractNumber = Path.Combine(fileFolderPathWithContractNumber, importedAction.MediaType);
                System.IO.Directory.CreateDirectory(fileFolderPathWithContractNumber);
                //ok, now we need to download the actual file
                using (var client = new WebClient())
                {
                    string remoteUri = importedAction.PictureURL;
                    string fileName = fileFolderPathWithContractNumber +  "\\" + importedAction.PictureURL.Substring(importedAction.PictureURL.Length - 36,32).ToString() + ".jpg";
                    string myStringWebResource = "";
                    // Create a new WebClient instance.
                    using (WebClient myWebClient = new WebClient())
                    {
                        myStringWebResource = remoteUri;
                        Uri myUri = new Uri(myStringWebResource, UriKind.Absolute);
                        // Download the Web resource and save it into the current filesystem folder.
                         myWebClient.DownloadFileAsync(myUri, fileName);
                    }

                    //var gps = ImageMetadataReader.ReadMetadata(fileName)
                    //         .OfType<GpsDirectory>()
                    //         .FirstOrDefault();

                    //var location = gps.GetGeoLocation();
                   



                }
            }

        }
        [Route("testSendEmail")]
        [HttpPost]
        public async Task testSendEmail(EmailData emailData)
        {
            StreamReader strSummary = new StreamReader(SummaryTemplatePDF);
            MailText = strSummary.ReadToEnd();
            strSummary.Close();
            MailText = MailText.Replace("[[footerImage]]", "<img src=\"cid:" + contentID + "\">");
            emailData.htmlToSend = MailText;

            Task.Run( sendEmail(emailData));
        }

        [Route("sendStrikeRateReports")]
        [HttpPost]

        public async Task sendStrikeRateReports(EmailData emailData)
        {

            


            //StreamReader strSummary = new StreamReader(SummaryTemplatePDF);
            //MailText = strSummary.ReadToEnd();
            //strSummary.Close();
            //MailText = MailText.Replace("[[InformationSpan]]", emailData.textToSend);
            //MailText = MailText.Replace("[[footerImage]]", "<img src=\"cid:" + contentID + "\">");
            //emailData.htmlToSend = MailText;



            //string pdfFilePath = pathForStrikeRatePDF + "\\" + emailData.subject + ".pdf";
            ////sendEmail(emailData);
            //LetsConvertPdf(emailData.textToSend,ref pdfFilePath);
            //List<string> strAttachments = new List<string>();
            //strAttachments.Add(pdfFilePath);
            //emailData.emailAttachements = strAttachments;
            ////byte[] pdfByteArray = PdfSharpConvert(emailData.htmlToSend);

            ////System.IO.File.WriteAllBytes(pdfFilePath, pdfByteArray);

            //sendEmail(emailData);
        }
        [Route("sendEmail")]
        public Action sendEmail(EmailData emailData)
        {
            List<string> pdfFilesToAttach = new List<string>();
            //we will have to change things around here a bit

            //we need to check for the html template, and if empty, use default.
            StreamReader strSummary = new StreamReader(emailData.htmlTemplate);
            MailText = strSummary.ReadToEnd();
            strSummary.Close();

            //first we need to do the pdfs

            //now, lets loop and change values
            foreach(ReplacementValues replacementValues in emailData.replacementValuesHtml)
            {
                MailText = MailText.Replace(replacementValues.StringToFind, replacementValues.valueToReplaceWith);
            }
            MailText = MailText.Replace("[[footerImage]]", "<img src=\"cid:" + contentID + "\">");
            //now check for the PDF Stuff. there might be multiple pdfs to deal with

            foreach (PDFAttachmentDetails pDFAttachmentDetails in emailData.PDFAttachmentDetails)
            {
                //lets generate the pdf here
                try
                {
                    emailData.emailAttachements.Add(generatePDFFile(pDFAttachmentDetails));
                }
                catch(Exception ex)
                {
                     ExceptionController.HandleException(ex);
                }
               
            }

            MailMessage mail = new MailMessage(emailData.sender, emailData.recipient)
            {
                Subject = emailData.subject,
                IsBodyHtml = true
            };
            mail.Attachments.Add(FooterAttachment);
            mail.Body = MailText;

            foreach(string strAttachment in emailData.emailAttachements)
            {
                Attachment attachment = new Attachment(strAttachment);
                mail.Attachments.Add(attachment);
            }

            foreach(string strCC in emailData.carbonCopies)
            {
                mail.CC.Add(strCC);
            }
            smtpClient.Send(mail);
            return null;
        }
        private static void InitializeSMTP()
        {

            smtpClient.Host = mailServer.HostName;
            smtpClient.Port = mailServer.Port;
            smtpClient.Credentials = new NetworkCredential(
            mailServer.Username, mailServer.Password);
            smtpClient.EnableSsl = true;

        }
        private static void GetMailServerDetails()
        {
            try
            {
                //ConnectionStrings["Test"].ConnectionString;
                
                    mailServer.MailServerID = "FF09A082-6C48-411B-B60F-1BA147502424";
                    mailServer.HostName = "za-smtp-outbound-1.mimecast.co.za";
                    mailServer.Port = 25;
                    mailServer.Ssl = "1";
                    mailServer.Username = "<EMAIL>";
                    mailServer.Password = "wSay42-mN8W7";

                    //ok, once we have the mail server details, we can send messages


            }
            catch (Exception ex)
            {
                 //ExceptionController.HandleException(ex);
            }
        }

         private string generatePDFFile(PDFAttachmentDetails pDFAttachmentDetails)
         {
            StreamReader strSummary = new StreamReader(StrikeRateReportPDFTemplate);
            System.IO.Directory.CreateDirectory(pathForPDFSaving + pDFAttachmentDetails.pdfFolder);
            string path = pathForPDFSaving + pDFAttachmentDetails.pdfFolder + "\\" + pDFAttachmentDetails.pdfName;
            string pdfString = strSummary.ReadToEnd();
            strSummary.Close();

            foreach (ReplacementValues replacementValues in pDFAttachmentDetails.replacementValuesPDF)
            {
                pdfString = pdfString.Replace(replacementValues.StringToFind, replacementValues.valueToReplaceWith);
            }
            SelectPdf.HtmlToPdf converter = new SelectPdf.HtmlToPdf();
            SelectPdf.PdfDocument doc = converter.ConvertHtmlString(pdfString);
            try
            {
                doc.Save(path);
            }
            catch(Exception ex)
            {
                //lets try and generate attribute
                path = path.Replace(".pdf", System.DateTime.Now.Ticks + ".pdf");
                doc.Save(path);
                 ExceptionController.HandleException(ex);
            }

            doc.Close();

            return path;
        }

        public static void LetsConvertPdf(string textToSend, ref string path)
        {

            StreamReader strSummary = new StreamReader(StrikeRateReportPDFTemplate);
            MailText = strSummary.ReadToEnd();
            strSummary.Close();
            MailText = MailText.Replace("[[InformationSpan]]", textToSend);

            //byte[] imageArray = System.IO.File.ReadAllBytes(pathForFooter);
            //string base64ImageRepresentation = Convert.ToBase64String(imageArray);

            SelectPdf.HtmlToPdf converter = new SelectPdf.HtmlToPdf();
            SelectPdf.PdfDocument doc = converter.ConvertHtmlString(MailText);
            try
            {
                doc.Save(path);
            }
            catch(Exception ex)
            {
                //lets try and generate attribute
                path = path.Replace(".pdf", System.DateTime.Now.Ticks + ".pdf");
                doc.Save(path);
                //ExceptionController.HandleException(ex);
            }
            
            doc.Close();

            //// instantiate a html to pdf converter object
            //SelectPdf.HtmlToPdf converter = new SelectPdf.HtmlToPdf();

            //// set converter options


            //// create a new pdf document converting an url
            //PdfDocument doc = converter.ConvertHtmlString(html);
            //PdfTemplate template = doc.AddTemplate(doc.Pages[0].ClientRectangle);
            //PdfImageElement img = new PdfImageElement(
            //    doc.Pages[0].ClientRectangle.Width - 300,
            //    doc.Pages[0].ClientRectangle.Height - 150, pathForFooter);
            //img.Transparency = 50;
            //template.Background = true;
            //template.Add(img);
            //// save pdf document
            //doc.Save(path);

            //// close pdf document
            //doc.Close();
        }

        [Route("GetImageFromServer")]
        [HttpGet]
        public IActionResult GetImageFromServer(string imagePath)
        {

            string ImagePathServer = Path.Combine(pathForImages, imagePath);

            Byte[] b = System.IO.File.ReadAllBytes(ImagePathServer);   // You can use your own method over here.         
            return File(b, "image/png");
        }



        [Route("ImportGraniteExcelFiles")]
        [HttpPost]
        public async Task<object> ImportGraniteExcelFiles(string fileName)
        {
            try
            {
                fileName = @"C:\Users\<USER>\Documents\Stock.xlsx";

                string fileNameCorrectLocations = @"C:\Users\<USER>\Documents\correctStores.xlsx";

                List<StockSummary> lstStockSummary = new List<StockSummary>();

                List<MasterItemInventoryItem> lstMasterIteminventoryItems = new List<MasterItemInventoryItem>();

                DataTable dt = new DataTable();
                using (XLWorkbook workbook = new XLWorkbook(fileName))
                {
                    IXLWorksheet worksheet = workbook.Worksheet(1);
                    bool FirstRow = true;
                    //Range for reading the cells based on the last cell used.  
                    string readRange = "1:1";
                    foreach (IXLRow row in worksheet.RowsUsed())
                    {
                        //If Reading the First Row (used) then add them as column name  
                        if (FirstRow)
                        {
                            //Checking the Last cellused for column generation in datatable  
                            readRange = string.Format("{0}:{1}", 1, row.LastCellUsed().Address.ColumnNumber);
                            foreach (IXLCell cell in row.Cells(readRange))
                            {
                                dt.Columns.Add(cell.Value.ToString());
                            }
                            FirstRow = false;
                        }
                        else
                        {
                            //Adding a Row in datatable  
                            dt.Rows.Add();
                            int cellIndex = 0;
                            //Updating the values of datatable  
                            foreach (IXLCell cell in row.Cells(readRange))
                            {
                                dt.Rows[dt.Rows.Count - 1][cellIndex] = cell.Value.ToString();
                                cellIndex++;
                            }
                        }
                    }
                }


                DataTable dtCorrectLocations = new DataTable();
                using (XLWorkbook workbook = new XLWorkbook(fileNameCorrectLocations))
                {
                    IXLWorksheet worksheet = workbook.Worksheet(3);
                    bool FirstRow = true;
                    //Range for reading the cells based on the last cell used.  
                    string readRange = "1:1";
                    foreach (IXLRow row in worksheet.RowsUsed())
                    {
                        //If Reading the First Row (used) then add them as column name  
                        if (FirstRow)
                        {
                            //Checking the Last cellused for column generation in datatable  
                            readRange = string.Format("{0}:{1}", 1, row.LastCellUsed().Address.ColumnNumber);
                            foreach (IXLCell cell in row.Cells(readRange))
                            {
                                dtCorrectLocations.Columns.Add(cell.Value.ToString());
                            }
                            FirstRow = false;
                        }
                        else
                        {
                            try
                            {
                                dtCorrectLocations.Rows.Add();
                                int cellIndex = 0;
                                //Updating the values of datatable  
                                foreach (IXLCell cell in row.Cells(readRange))
                                {
                                    dtCorrectLocations.Rows[dtCorrectLocations.Rows.Count - 1][cellIndex] = cell.Value.ToString();
                                    cellIndex++;
                                }
                            }
                            catch
                            {

                            }
                            //Adding a Row in datatable  
                          
                        }
                    }
                }
                List<MasterItem> lstMasterItems = new List<MasterItem>();

                List<Store> lstStores = new List<Store>();

                List<InstallationTeam> lstInstallationTeams = new List<InstallationTeam>();

                List<Warehouse> lstWarehouses = new List<Warehouse>();

                List<Shelf> lstShelves = new List<Shelf>();




                using (var db = new NovaDBContext())
                {
                    lstMasterItems = db.MasterItems.ToList();
                    lstInstallationTeams = db.InstallationTeam.ToList();
                    lstStores = db.Store
                        .Include(x => x.Region)
                        .ThenInclude(y => y.Chain)
                        .Where(x => x.Region.Chain.Dormant == false)
                        .ToList();
                    lstWarehouses = db.Warehouse.Where(x => x.WarehouseCode != null).ToList();
                    lstShelves = db.Shelves.Include(x => x.Warehouse).ToList();
                }

                Parallel.ForEach(dt.Rows.Cast<DataRow>(), dr =>
                {
                    //here we need to conver the rows


                    StockSummary stockSummary = new StockSummary();
                    MasterItemInventoryItem itemInventoryItem = new MasterItemInventoryItem();
                    try
                    {
                        //lets try to add it here

                        //itemInventoryItem.Code = dr["Code"].ToString();
                        //itemInventoryItem.qty = Convert.ToInt32(dr["Qty On Hand"].ToString());
                        //itemInventoryItem.Location = dr["Current Location"].ToString();
                        //itemInventoryItem.Item = dr["Description"].ToString();
                        itemInventoryItem.Barcode = dr["Barcode"].ToString();
                        itemInventoryItem.ContractNumber = "";
                        itemInventoryItem.CreatedBy = "<EMAIL>";
                        itemInventoryItem.CreationDate = System.DateTime.Now;
                        itemInventoryItem.InstallationTeamId = lstInstallationTeams.Where(x => x.InstallationTeamName.ToLower().Replace(" team ", "").Replace("01", "1").Replace("01", "1").Replace("02", "2").Replace("03", "3").Replace("04", "4").Replace("05", "5").Replace("06", "6").Replace("07", "7").Replace("08", "8").Replace("09", "9") == dr["Current Location"].ToString().ToLower()).Select(x => x.InstallationTeamId).FirstOrDefault();
                        itemInventoryItem.MasterItemId = lstMasterItems.Where(x => x.MasterItemName.ToLower() == dr["Description"].ToString().ToLower()).Select(x => x.Id).FirstOrDefault();
                        itemInventoryItem.StoreId = lstStores.Where(x => x.Region.Chain.ChainName.ToLower() + " " + x.StoreName.ToLower().Trim() == dr["Current Location"].ToString().ToLower().Trim()).Select(x => x.StoreId).FirstOrDefault(); 
                        itemInventoryItem.WarehouseId = lstWarehouses.Where(x => x.WarehouseCode.ToLower() == dr["Current Location"].ToString().ToLower()).Select(x => x.WarehouseId).FirstOrDefault();

                        itemInventoryItem.ShelfId = lstShelves.Where(x => x.ShelfCode.ToLower() == dr["Current Location"].ToString().ToLower()).Select(x => x.ShelfId).FirstOrDefault();


                       if(dr["Current Location"].ToString().ToLower().Contains("clicks"))
                        {
                            if(itemInventoryItem.StoreId == 0)
                            {
                                itemInventoryItem.StoreId = lstStores.Where(x => x.Region.Chain.ChainName.ToLower() + " " + x.StoreName.ToLower().Trim() == dr["Current Location"].ToString().Trim().ToLower() + " + pharm").Select(x => x.StoreId).FirstOrDefault();
                                if(itemInventoryItem.StoreId == 0)
                                {
                                    //find the correct location
                                    var result = dtCorrectLocations
                                    .AsEnumerable()
                                    .Where(myRow => myRow.Field<string>("Current Location") == dr["Current Location"].ToString());
                                   // results[0][""];
                                    foreach(DataRow mydr in result)
                                    {
                                        string value = mydr["Correct Location"].ToString();
                                        if(itemInventoryItem.StoreId == 0)
                                        {
                                            itemInventoryItem.StoreId = lstStores.Where(x => x.StoreName.ToLower().Trim() == value.ToString().Trim().ToLower()).Select(x => x.StoreId).FirstOrDefault();
                                        }
                                        
                                        
                                    }
                                   

                                }


                            }

                            if (itemInventoryItem.StoreId == 0)
                            {
                                itemInventoryItem.StoreId = lstStores.Where(x => x.Region.Chain.ChainName.ToLower() + " " + x.StoreName.ToLower().Trim() == dr["Current Location"].ToString().Trim().ToLower() + " mall + pharm").Select(x => x.StoreId).FirstOrDefault();
                            }
                        }

                        if (dr["Current Location"].ToString().ToLower().Contains("pnp"))
                        {
                            if (itemInventoryItem.StoreId == 0)
                            {
                                itemInventoryItem.StoreId = lstStores.Where(x => x.Region.Chain.ChainName.ToLower().Replace("pnp super liquor","pnp").Replace("pnp super", "pnp").Replace("fslq","lq fs").Replace("pnp hyper", "pnp").Replace(" ", "") +  x.StoreName.ToLower().Replace(" ", "") == dr["Current Location"].ToString().ToLower().Replace(" ","") + "").Select(x => x.StoreId).FirstOrDefault();
                                if (itemInventoryItem.StoreId == 0)
                                {
                                    //find the correct location
                                    var result = dtCorrectLocations
                                   .AsEnumerable()
                                   .Where(myRow => myRow.Field<string>("Current Location") == dr["Current Location"].ToString());

                                    foreach (DataRow mydr in result)
                                    {
                                        string value = mydr["Correct Location"].ToString();
                                        itemInventoryItem.StoreId = lstStores.Where(x => x.StoreName.ToLower().Trim() == value.ToString().Trim().ToLower()).Select(x => x.StoreId).FirstOrDefault();
                                    }
                                }
                            }
                        }

                        if (itemInventoryItem.StoreId == 0)
                        {
                            var result = dtCorrectLocations
                                 .AsEnumerable()
                                 .Where(myRow => myRow.Field<string>("Current Location") == dr["Current Location"].ToString());

                            foreach (DataRow mydr in result)
                            {
                                string value = mydr["Correct Location"].ToString();
                                itemInventoryItem.StoreId = lstStores.Where(x => x.StoreName.ToLower().Trim() == value.ToString().Trim().ToLower()).Select(x => x.StoreId).FirstOrDefault();
                            }
                        }

                            if (itemInventoryItem.InstallationTeamId == 0)
                        {
                            itemInventoryItem.InstallationTeamId = null;
                        }

                        if (itemInventoryItem.StoreId == 0)
                        {
                            itemInventoryItem.StoreId = null;
                        }

                        if(itemInventoryItem.ShelfId != 0)
                        {
                            //set the warehouse as well
                            itemInventoryItem.WarehouseId = lstShelves.Where(x => x.ShelfCode.ToLower() == dr["Current Location"].ToString().ToLower()).Select(x => x.WarehouseId).FirstOrDefault();
                        }
                        if (itemInventoryItem.ShelfId == 0)
                        {
                            itemInventoryItem.ShelfId = null;
                        }
                            if (itemInventoryItem.WarehouseId == 0)
                        {
                            itemInventoryItem.WarehouseId = null;
                        }
                        lstMasterIteminventoryItems.Add(itemInventoryItem);

                    }
                    catch (Exception ex)
                    {
                        // await ExceptionController.HandleException(ex);
                        string message = ex.Message;
                    }


                });

                //now we can add them to the database


                //foreach (StockSummary summary in lstStockSummary)
                //{
                //    //lets check
                //    string a = summary.Code;
                //    //lets find the master item id


                //}

                //lets time 1000 to see how long it will take

                //List<StockSummary> stockSummariesThousand = new List<StockSummary>();
                //stockSummariesThousand = lstStockSummary.Where(x => x.Location.ToLower() == "nwh").ToList();

                //Parallel.ForEach(stockSummariesThousand, summary =>
                //{
                //    summary.MasterItemId = lstMasterItems.Where(x => x.MasterItemName.ToLower() == summary.Item.ToLower()).Select(x => x.Id).FirstOrDefault();
                //    summary.InstallationTeamId = lstInstallationTeams.Where(x => x.InstallationTeamName.ToLower().Replace(" team ","").Replace("01","1").Replace("01", "1").Replace("02", "2").Replace("03", "3").Replace("04", "4").Replace("05", "5").Replace("06", "6").Replace("07", "7").Replace("08", "8").Replace("09", "9") == summary.Location.ToLower()).Select(x => x.InstallationTeamId).FirstOrDefault();
                //    summary.StoreId = lstStores.Where(x => x.StoreName.ToLower() == summary.Location.ToLower()).Select(x => x.StoreId).FirstOrDefault();
                //    summary.WarehouseId = lstWarehouses.Where(x => x.WarehouseCode.ToLower() == summary.Location.ToLower()).Select(x => x.WarehouseId).FirstOrDefault();
                //});

                //int mycount = stockSummariesThousand.Where(x => x.StoreId != 0).Count();
                //int mycountnew = stockSummariesThousand.Where(x => x.WarehouseId != 0).Count();
                //int mycountnewnew = stockSummariesThousand.Where(x => x.InstallationTeamId != 0).Count();
                List<MasterItemInventoryItem> lstNoData = lstMasterIteminventoryItems.Where(x => x.MasterItemId != 0 && x.InstallationTeamId == null && x.StoreId == null && x.WarehouseId == null).ToList();

                //write no data to exception file
                DataTable dtException = ToDataTable(lstNoData);
                string fileNameToExport = pathForExcelFiles + "\\Sample.xlsx";
                using (XLWorkbook wb = new XLWorkbook())
                {
                    //Add DataTable in worksheet  
                    wb.Worksheets.Add(dtException);
                    using (MemoryStream stream = new MemoryStream())
                    {
                        wb.SaveAs(fileNameToExport);
                       // var fileStream = new FileStream(fileNameToExport, FileMode.Create, FileAccess.Write);
                       // stream.CopyTo(fileStream);
                       // fileStream.Dispose();
                        //Return xlsx Excel File  
                        //(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                    }
                }


                using (var db = new NovaDBContext())
                {
                    db.MasterItemInventoryItems.AddRange(lstMasterIteminventoryItems.Where(x => x.MasterItemId != 0));
                    //&& (x.InstallationTeamId == 55 || x.InstallationTeamId == 3 || x.InstallationTeamId == 60 ||  x.StoreId != null)).ToList());
                    db.SaveChanges();
                }
                //so, needless to say, not lots are matching up. Let's do some funly logic here.
                string abc = "";

                //List<StockSummary> lstNationalWarehouseItems = stockSummariesThousand.Where(x => x.WarehouseId == 236).ToList();

                //ok, once we have the data, we can look at importing it
                MasterItemInventoryItem masterItemInventoryItem = new MasterItemInventoryItem();
                
                return null;
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("ImportUserList")]
        [HttpPost]
        public async Task<object> ImportUserList(string fileName)
        {
            try
            {
                fileName = @"C:\Users\<USER>\Downloads\UsersToImport.xlsx";

                List<UsersImported> lstUsersImported = new List<UsersImported>();

               

                DataTable dt = new DataTable();
                using (XLWorkbook workbook = new XLWorkbook(fileName))
                {
                    IXLWorksheet worksheet = workbook.Worksheet(1);
                    bool FirstRow = true;
                    //Range for reading the cells based on the last cell used.  
                    string readRange = "1:1";
                    foreach (IXLRow row in worksheet.RowsUsed())
                    {
                        //If Reading the First Row (used) then add them as column name  
                        if (FirstRow)
                        {
                            //Checking the Last cellused for column generation in datatable  
                            readRange = string.Format("{0}:{1}", 1, row.LastCellUsed().Address.ColumnNumber);
                            foreach (IXLCell cell in row.Cells(readRange))
                            {
                                dt.Columns.Add(cell.Value.ToString());
                            }
                            FirstRow = false;
                        }
                        else
                        {
                            //Adding a Row in datatable  
                            dt.Rows.Add();
                            int cellIndex = 0;
                            //Updating the values of datatable  
                            foreach (IXLCell cell in row.Cells(readRange))
                            {
                                dt.Rows[dt.Rows.Count - 1][cellIndex] = cell.Value.ToString();
                                cellIndex++;
                            }
                        }
                    }
                }
               
                Parallel.ForEach(dt.Rows.Cast<DataRow>(), dr =>
                {
                    //here we need to conver the rows


                    UsersImported user = new UsersImported();
                    
                    try
                    {
                        //lets try to add it here

                        //itemInventoryItem.Code = dr["Code"].ToString();
                        //itemInventoryItem.qty = Convert.ToInt32(dr["Qty On Hand"].ToString());
                        //itemInventoryItem.Location = dr["Current Location"].ToString();
                        //itemInventoryItem.Item = dr["Description"].ToString();
                        user.FirstName = dr["First Name"].ToString();
                        user.Surname = dr["Surname"].ToString();
                        user.TeamName = dr["Team Name"].ToString();
                        user.CellNumber = dr["Personal Cell Nr"].ToString();
                       

                       
                        lstUsersImported.Add(user);

                    }
                    catch (Exception ex)
                    {
                        // await ExceptionController.HandleException(ex);
                    }


                });


              


                foreach (UsersImported user in lstUsersImported)
                {
                    string abcd = user.FirstName;
                  
                }

                //List<StockSummary> lstNationalWarehouseItems = stockSummariesThousand.Where(x => x.WarehouseId == 236).ToList();

                //ok, once we have the data, we can look at importing it
               

                return null;
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }
        public MemoryStream ImageToByteArray(System.Drawing.Image imageIn)
        {
            EncoderParameter qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 15L);
          
            EncoderParameters encoderParams = new EncoderParameters(1);
            encoderParams.Param[0] = qualityParam;
            ImageFormat format = imageIn.RawFormat;
            ImageCodecInfo codec = ImageCodecInfo.GetImageDecoders().First(c => c.FormatID == format.Guid);
            string mimeType = codec.MimeType;

            MemoryStream memoryStream = new MemoryStream();
            imageIn.Save(memoryStream, codec, encoderParams);
            return memoryStream;
            //using (var ms = new MemoryStream())
            //{

               
            //    Bitmap bm = new Bitmap(ms);
            //    return bm;
            //}
        }



        public MemoryStream ImageToByteArrayThumb(System.Drawing.Image imageIn)
        {

            EncoderParameter qualityParam = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, 35L);

            EncoderParameters encoderParams = new EncoderParameters(1);
            encoderParams.Param[0] = qualityParam;
            ImageFormat format = ImageFormat.Jpeg;
            ImageCodecInfo codec = ImageCodecInfo.GetImageDecoders().First(c => c.FormatID == format.Guid);
            string mimeType = codec.MimeType;

            MemoryStream memoryStream = new MemoryStream();
            imageIn.Save(memoryStream, codec, encoderParams);
            return memoryStream;
            //using (var ms = new MemoryStream())
            //{


            //    Bitmap bm = new Bitmap(ms);
            //    return bm;
            //}
        }

        [Route("GenerateExcelFile")]
        [HttpPost]
        public async Task<object> GenerateExcelFile(GenerateExcelFileData excelFileData)
        {
            try
            {
             
                //lets have a look at the data
                var json = JsonConvert.SerializeObject(excelFileData.Data);
                DataTable dt = (DataTable)JsonConvert.DeserializeObject(json, (typeof(DataTable)));
                IXLWorksheet worksheet;
                XLWorkbook wb = new XLWorkbook();
                try
                {
                     worksheet = wb.Worksheets.Add(dt, "Picture");
                    worksheet.Cell("C2").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                    worksheet.Cell("C2").Style.Border.OutsideBorderColor = XLColor.Blue;
                }
                catch
                {

                }
               
                IXLWorksheet worksheetTwo = wb.Worksheets.Add("PictureReport");
                int currentRow = 2;
                int currentCell = 1;

                string[] columnNames = dt.Columns.Cast<DataColumn>()
                                     .Select(x => x.ColumnName)
                                     .Where(x => x != "actionId" && x != "irCodeID")
                                     .ToArray();

                foreach (string columnName in columnNames)
                {
                    worksheetTwo.Cell(1, currentCell).Value = char.ToUpperInvariant(columnName[0]) + columnName.Substring(1);
                    currentCell += 1;
                }
                currentCell = 1;
           


                foreach (DataRow dr in dt.Rows)
                {
                  



                    foreach (string columnName in columnNames)
                    {
                        if (columnName.ToLower() == "picture" && excelFileData.includePicture)
                        {
                            //var imagePath = @"\\************\images\JS020002\94616772-a5fd-4483-c3ad-08d8f788806f-16179618972563280423607572183757.jpg";
                            if (dr["imageURL"].ToString() != "")
                            {
                                try
                                {
                                    var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImagesLQ/?imageURL=\\", "").ToString();
                                    //var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://localhost:44334/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();
                                    Image myImage = Image.FromFile(imagePath, true);


                                    Image thumb = myImage.GetThumbnailImage(1280, 720, () => false, IntPtr.Zero);



                                    // Bitmap bitmapThumb = new Bitmap(thumb,);

                                    //var image = worksheetTwo.AddPicture(ImageToByteArray(myImage)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);

                                    //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.3);

                                    //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).Height = 200;

                                    

                                    IXLPicture pic = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb));
                                    pic.MoveTo(worksheetTwo.Cell(currentRow, currentCell));
                                    double height = (5.32 * 96) / 2.54;
                                    double witdh = (4.71 * 96) / 2.54;
                                  
                                    pic.Width = (int)Math.Round(witdh);
                                    pic.Height = (int)Math.Round(height);



                                    //.ScaleWidth(0.3);
                                    worksheetTwo.Column(currentCell).Width = 25;
                                    //worksheetTwo.Row(currentRow).Height = image.Height;

                                    worksheetTwo.Row(currentRow).Height = 151.80;
                                }
                                catch(Exception ex)
                                {
                                    string error = ex.Message;
                                    worksheetTwo.Cell(currentRow, currentCell).Value = "";
                                }
                                // Write the byte array to the other FileStream.
                                
                                
                            }
                            else
                            {
                                worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                            }


                        }
                        else
                        {
                            if(columnName.ToLower().Contains("url"))
                            {

                                //check for store name.
                                string storename = "";
                                try
                                {
                                    storename = dr["store"].ToString();
                                }
                                catch
                                {
                                    storename = "";
                                }


                                if(dr[columnName].ToString() != "")
                                { 
                                worksheetTwo.Cell(currentRow, currentCell).Value = "View Picture";

                                worksheetTwo.Cell(currentRow, currentCell).Hyperlink = new XLHyperlink(dr[columnName].ToString() + "&storename=" + storename.Replace("GetImagesLQ", "GetImages"));
                                worksheetTwo.Cell(currentRow, currentCell).Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                                }
                                else
                                {
                                    worksheetTwo.Cell(currentRow, currentCell).Value = "";
                                }
                            }
                            else
                            {
                                worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                                worksheetTwo.Cell(currentRow, currentCell).Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                            }

                          
                           
                        }

                        currentCell += 1;
                    }
                    currentCell = 1;
                    currentRow += 1;

                }
                //Adjusting size of the excel cell as per requirement 
                //worksheet.Column(3).Width = 60;
                //worksheet.Row(3).Height = 50;



                //Scaling down image as per our cell size


                //Formating the cell with border and color
              
                string email = "";
                if(User != null)
                {
                    email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                }
                else
                {
                    email = excelFileData.emailToSendTo;
                }
               
                string filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + excelFileData.fileName;
                //send an email now
                try
                {
                    
                    //just test logic quick
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + System.DateTime.Now.ToShortDateString().Replace("/", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }
                catch
                {
                    //generate new name
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + System.DateTime.Now.ToShortDateString().Replace("\\", "-") + System.Guid.NewGuid().ToString() + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }


                try
                {
                    //now we can finally send the email.(Actually, we still need to do the managers, but for now lets send it to me).
                    EmailData emailData = new EmailData();
                    //emailData.carbonCopies.Add("<EMAIL>");
                    List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                    ReplacementValues replacement = new ReplacementValues();
                    replacement.StringToFind = "[[FullText]]";
                    if (!excelFileData.saveAsAttachment)
                    {
                        replacement.valueToReplaceWith = "Your report is now ready for Download, please click on the link to download it : <a href='" + filenameToSave + " ' download target='_blank'>" + "Download Your Report" + "</a>";
                    }
                    else
                    {
                        replacement.valueToReplaceWith = "Please find your report";
                    }
                       
                    replacementValues.Add(replacement);
                    emailData.replacementValuesHtml = replacementValues;
                    if(excelFileData.saveAsAttachment)
                    {
                        emailData.emailAttachements = new List<string> { filenameToSave };
                    }
                    if(excelFileData.Subject == "")
                    {
                        emailData.subject = "Picture Report";
                    }
                    else
                    {
                        emailData.subject = excelFileData.Subject;
                    }
                  
                    emailData.recipient = email;
                    await Task.Run(sendEmail(emailData));
                }
                catch (Exception ex)
                {
                    return ex.Message;
                }




                return null;
            }
            catch(Exception ex)
            {
                return ex.Message;
            }
           
        }


        [Route("GeneratePictureReport")]
        [HttpPost]
        public async Task<object> GeneratePictureReport(GenerateExcelFileData excelFileData)
        {
            try
            {

                //lets have a look at the data
                var json = JsonConvert.SerializeObject(excelFileData.Data);
                DataTable dt = (DataTable)JsonConvert.DeserializeObject(json, (typeof(DataTable)));

                XLWorkbook wb = new XLWorkbook();
                //IXLWorksheet worksheet = wb.Worksheets.Add(dt, "Picture");
                IXLWorksheet worksheetTwo = wb.Worksheets.Add("PictureReport");
                int currentRow = 2;
                int currentCell = 1;

                string[] columnNames = dt.Columns.Cast<DataColumn>()
                                     .Select(x => x.ColumnName)
                                     .ToArray();

                //lets create new cells and rows first.
                worksheetTwo.Cell(1, 1).Value = "Picture Report";
                worksheetTwo.Cell(1,1).Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                ///worksheetTwo.Range("A1:O1").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                //worksheetTwo.Range("A1:O1").Style.Border.OutsideBorderColor = XLColor.Blue;
                //worksheetTwo.Cell(1, 1).Style.Font.FontColor = XLColor.DarkBlue;
                worksheetTwo.Cell(1, 1).Style.Font.FontSize = 16;
                //lets not worry about the color just yet.
                int AmountOfRowsPerRow = 9;
                int amountOfColumns = 2;
                //ok, lets figure it out quickly.
                //merge code below
                //worksheetTwo.Range(worksheet.Cell(row, col++), worksheet.Cell(row, col++)).Row(1).Merge();

                //first, lets create the stuff.

                //for now lets merge 14 columns for testing
                worksheetTwo.Cell(1, 1).Value = "Picture Report";
                //worksheetTwo.Range("A1:O1").Row(1).Merge();

                //we now have the heading, lets do the rest.
                int currentRowToBeOn = 2;
                int firstRowsCount = 3;
                int secondRowCount = 9;
                currentCell = 1;
                foreach (DataRow dr in dt.Rows)
                {
                    int i = 0;

                    //ok, here we need to just add correct values and format a bit more.
                    while ( i < firstRowsCount)
                    {
                        //we
                        if(i == 0)
                        {
                            worksheetTwo.Cell(currentRow,1).Value = "Week";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow,2).Value = "Week Value";
                            worksheetTwo.Cell(currentRow, 3).Value = "Date Opened";
                            worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 4).Value = dr["dateOpened"].ToString();
                            worksheetTwo.Cell(currentRow, 5).Value = "Region";
                            worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheetTwo.Cell(currentRow, 6).Value = dr["Region"].ToString();

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 6)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        //we
                        if (i == 1)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Chain";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["Chain"].ToString();
                            worksheetTwo.Cell(currentRow, 3).Value = "Date Completed";
                            worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 4).Value = dr["dateClosed"].ToString();
                            worksheetTwo.Cell(currentRow, 5).Value = "Team";
                            worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheetTwo.Cell(currentRow, 6).Value = dr["Team"].ToString();

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 6)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        if (i == 2)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Store";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["store"].ToString();
                            worksheetTwo.Cell(currentRow, 3).Value = "Hours Worked";
                            worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 4).Value = "Hours Worked Value";
                            worksheetTwo.Cell(currentRow, 5).Value = "Date";
                            worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheetTwo.Cell(currentRow, 6).Value = dr["scheduleDate"].ToString();

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 6)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        currentRow++;
                        i++;
                    }

                    //now we need to create the rest
                    //create an empty row
                    currentRow += 1;
                    //now we can create the rest of the report.
                    //ok, we know the data that is contained, we need to make more rows here
                    //create 3 rows, keep a counter
                    i = 0;
                    bool hasImageValid = true;
                    while (i < secondRowCount)
                    {
                        //we
                        if (i == 0)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Category";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["category"].ToString();
                            //worksheetTwo.Cell(currentRow, 3).Value = "This will be a picture";
                            try
                            {
                                var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImagesLQ/?imageURL=\\", "").ToString();

                                //var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://localhost:44334/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();


                                Image myImage = Image.FromFile(imagePath, true);


                                Image thumb = myImage.GetThumbnailImage(1280, 720, () => false, IntPtr.Zero);



                                // Bitmap bitmapThumb = new Bitmap(thumb,);

                                //var image = worksheetTwo.AddPicture(ImageToByteArray(myImage)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);

                                //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.3).ScaleWidth(0.3);


                                //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, 3)).ScaleHeight(0.3).ScaleWidth(0.3);


                                IXLPicture pic = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb));
                                pic.MoveTo(worksheetTwo.Cell(currentRow, 3));
                                double height = (4.82 * 96) / 2.54;
                                double witdh = (8.57 * 96) / 2.54;

                                pic.Width = (int)Math.Round(witdh);
                                pic.Height = (int)Math.Round(height);


                                // worksheetTwo.Column(currentCell).Width = 60;
                                //worksheetTwo.Row(currentRow).Height = image.Height;

                            }
                            catch
                            {

                            }
                            // Write the byte array to the other FileStream.
                            



                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        //we
                        if (i == 1)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Media Type";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["mediaType"].ToString();


                        }
                        if (i == 2)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Action";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["action"].ToString();

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        if (i == 3)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "IR Category";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = "IR Category Value";

                        }
                        if (i == 4)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "IR Code";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["irCode"].ToString();

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        if (i == 5)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Contract";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["contract"].ToString();

                        }
                        if (i == 6)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Product";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["product"].ToString();

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        if (i == 7)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "IR Notes";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = dr["irCodeComment"].ToString();

                        }
                        if (i == 8)
                        {
                            worksheetTwo.Cell(currentRow, 1).Value = "Picture take on";
                            worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            worksheetTwo.Cell(currentRow, 2).Value = "Picture take on Value";

                            worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;


                           worksheetTwo.Range(worksheetTwo.Cell(currentRow - 8, 3), worksheetTwo.Cell(currentRow, 3)).Row(8).Merge();
                        }
                        currentRow++;
                        i++;
                    }

                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                }

                //foreach (string columnName in columnNames.OrderBy(x => x.ToString()))
                //{
                //    worksheetTwo.Cell(1, currentCell).Value = columnName;
                //    currentCell += 1;
                //}
                //currentCell = 1;
                //foreach (DataRow dr in dt.Rows)
                //{
                //    foreach (string columnName in columnNames.OrderBy(x => x.ToString()))
                //    {
                //        if (columnName.ToLower() == "imageurl")
                //        {
                //            //var imagePath = @"\\************\images\JS020002\94616772-a5fd-4483-c3ad-08d8f788806f-16179618972563280423607572183757.jpg";
                //            if (dr[columnName].ToString() != "")
                //            {
                //                // Write the byte array to the other FileStream.
                //                var imagePath = @"\\************\images\" + dr[columnName].ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();

                //                var image = worksheetTwo.AddPicture(imagePath).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);
                //                worksheetTwo.Column(currentCell).Width = 60;
                //                worksheetTwo.Row(currentRow).Height = image.Height;
                //            }
                //            else
                //            {
                //                worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                //            }


                //        }
                //        else
                //        {
                //            worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                //        }

                //        currentCell += 1;
                //    }
                //    currentCell = 1;
                //    currentRow += 1;

                //}
                //Adjusting size of the excel cell as per requirement 
                //worksheet.Column(3).Width = 60;
                //worksheet.Row(3).Height = 50;



                //Scaling down image as per our cell size


                //Formating the cell with border and color
                //  worksheet.Cell("C2").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                // worksheet.Cell("C2").Style.Border.OutsideBorderColor = XLColor.Blue;

                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                string filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + excelFileData.fileName;
                //send an email now
                try
                {
                    
                    //wb.SaveAs(filenameToSave);
                    //just test logic quick
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + System.DateTime.Now.ToShortDateString().Replace("/", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }
                catch
                {
                    //generate new name
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + System.DateTime.Now.ToShortDateString().Replace("\\", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }


                try
                {
                    //now we can finally send the email.(Actually, we still need to do the managers, but for now lets send it to me).
                    EmailData emailData = new EmailData();
                    //emailData.carbonCopies.Add("<EMAIL>");
                    List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                    ReplacementValues replacement = new ReplacementValues();
                    replacement.StringToFind = "[[FullText]]";
                    replacement.valueToReplaceWith = "Your report is now ready for Download, please click on the link to download it : <a href='" + filenameToSave + " ' download target='_blank'>" + "Download Your Report" + "</a>";
                    replacementValues.Add(replacement);
                    emailData.replacementValuesHtml = replacementValues;
                    //emailData.emailAttachements = new List<string> { filenameToSave };
                    emailData.subject = "Picture Report";
                    emailData.recipient = email;
                    await Task.Run(sendEmail(emailData));
                }
                catch (Exception ex)
                {
                    return ex.Message;
                }




                return null;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }


        [Route("GenerateMerchandisingPictureReport")]
        [HttpPost]
        public async Task<object> GenerateMerchandisingPictureReport(GenerateExcelFileData excelFileData)
        {
            try
            {

                //lets have a look at the data
                var json = JsonConvert.SerializeObject(excelFileData.Data);
                DataTable dt = (DataTable)JsonConvert.DeserializeObject(json, (typeof(DataTable)));

                XLWorkbook wb = new XLWorkbook();
                //IXLWorksheet worksheet = wb.Worksheets.Add(dt, "Picture");
                IXLWorksheet worksheetTwo = wb.Worksheets.Add("PictureReport");
                int currentRow = 2;
                int currentCell = 1;

                string[] columnNames = dt.Columns.Cast<DataColumn>()
                                     .Select(x => x.ColumnName)
                                     .ToArray();

                //lets create new cells and rows first.
                worksheetTwo.Cell(1, 1).Value = "Picture Report";
                worksheetTwo.Cell(1, 1).Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                ///worksheetTwo.Range("A1:O1").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                //worksheetTwo.Range("A1:O1").Style.Border.OutsideBorderColor = XLColor.Blue;
                //worksheetTwo.Cell(1, 1).Style.Font.FontColor = XLColor.DarkBlue;
                worksheetTwo.Cell(1, 1).Style.Font.FontSize = 16;
                //lets not worry about the color just yet.
                int AmountOfRowsPerRow = 9;
                int amountOfColumns = 2;
                //ok, lets figure it out quickly.
                //merge code below
                //worksheetTwo.Range(worksheet.Cell(row, col++), worksheet.Cell(row, col++)).Row(1).Merge();

                //first, lets create the stuff.

                //for now lets merge 14 columns for testing
                worksheetTwo.Cell(1, 1).Value = "Picture Report";
                //worksheetTwo.Range("A1:O1").Row(1).Merge();

                //we now have the heading, lets do the rest.
                int currentRowToBeOn = 2;
                int firstRowsCount = 3;
                int secondRowCount = 9;
                int thirdRowCount = 1;
                currentCell = 1;

                string currentStore = "";
                string previousStore = "";
                string previousProduct = "";
                bool isFirstStore = true;
                bool isFirstRun = true;
                int amountOfPictures = 0;
                foreach (DataRow dr in dt.Rows)
                {
                    int i = 0;
                    
                    if(previousStore == "")
                    {
                        previousStore = dr["Store"].ToString();
                        previousProduct = dr["Product"].ToString();
                        isFirstStore = true;
                    }



                    if(previousStore == dr["Store"].ToString() && dr["Product"].ToString() == previousProduct && !isFirstRun)
                    {
                        //this is one store data we need to deal with now
                        isFirstStore = false;
                    }
                    else
                    {
                        isFirstStore = true;
                    }
                    previousStore = dr["Store"].ToString();
                    previousProduct = dr["Product"].ToString();
                    isFirstRun = false;
                    
                    //ok, here we need to just add correct values and format a bit more.
                    while (i < firstRowsCount)
                    {
                        
                        if(isFirstStore)
                        {
                            if (i == 0)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Week";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["ScheduleDate"].ToString();
                                //worksheetTwo.Cell(currentRow, 3).Value = "Date Opened";
                                //worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 4).Value = dr["dateOpened"].ToString();
                                //worksheetTwo.Cell(currentRow, 5).Value = "Region";
                                //worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                                //worksheetTwo.Cell(currentRow, 6).Value = dr["Region"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            //we
                            if (i == 1)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Chain";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["Chain"].ToString();
                                //worksheetTwo.Cell(currentRow, 3).Value = "Date Completed";
                                //worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 4).Value = dr["dateClosed"].ToString();
                                //worksheetTwo.Cell(currentRow, 5).Value = "Team";
                                //worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                                //worksheetTwo.Cell(currentRow, 6).Value = dr["Team"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            if (i == 2)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Store";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["store"].ToString();
                                //worksheetTwo.Cell(currentRow, 3).Value = "Hours Worked";
                                //worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 4).Value = "Hours Worked Value";
                                //worksheetTwo.Cell(currentRow, 5).Value = "Date";
                                //worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                                //worksheetTwo.Cell(currentRow, 6).Value = dr["scheduleDate"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                        }
                        //we
                      
                        currentRow++;
                        i++;
                    }

                    //now we need to create the rest
                    //create an empty row
                    currentRow += 1;
                    //now we can create the rest of the report.
                    //ok, we know the data that is contained, we need to make more rows here
                    //create 3 rows, keep a counter
                    i = 0;
                    bool hasImageValid = true;
                    while (i < secondRowCount)
                    {
                        //we
                        if (i == 0)
                        {
                            if(isFirstStore)
                            { 
                                worksheetTwo.Cell(currentRow, 1).Value = "Category";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["category"].ToString();
                            }
                            //worksheetTwo.Cell(currentRow, 3).Value = "This will be a picture";
                          




                            //worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        //we
                        if(isFirstStore)
                        {
                            if (i == 1)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Media Type";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["mediaType"].ToString();


                            }
                            if (i == 2)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Did you pull a “dispo” report in-store?";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["isDispo"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            if (i == 3)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Are all lines on shelf and neat?";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["linesNeat"].ToString();

                            }
                            if (i == 4)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Are all lines priced?";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["pricedCorrect"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            if (i == 5)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Are the stock counts on the system correct?";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["stockCounts"].ToString();

                            }
                            if (i == 6)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Was there stock in the store room, if so did you pack it onto the shelf?";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["stockRoomStock"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            if (i == 7)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Did the store manager assist with aged stock?";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["stockAged"].ToString();

                            }
                            if (i == 8)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Contract";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr["contract"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;

                            }

                        }
                        if (i == 8)
                        {
                            //worksheetTwo.Cell(currentRow, 1).Value = "Picture take on";
                            //worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //worksheetTwo.Cell(currentRow, 2).Value = "Picture take on Value";

                            //worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;


                            //worksheetTwo.Range(worksheetTwo.Cell(currentRow - 8, 3), worksheetTwo.Cell(currentRow, 3)).Row(8).Merge();
                            
                            // Write the byte array to the other FileStream.



                        }
                        currentRow++;
                        i++;
                    }
                    i = 0;
                    while (i < thirdRowCount)
                    {
                        i++;
                        try
                        {
                            var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImagesLQ/?imageURL=\\", "").ToString();

                            //var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://localhost:44334/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();


                            Image myImage = Image.FromFile(imagePath, true);


                            Image thumb = myImage.GetThumbnailImage(1280, 720, () => false, IntPtr.Zero);

                            IXLPicture pic = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb));
                            if (isFirstStore)
                            {
                                pic.MoveTo(worksheetTwo.Cell(currentRow, 1));
                                amountOfPictures = 1;
                            }
                            else if (amountOfPictures == 1 && !isFirstStore)
                            {
                                currentRow = currentRow - 11;
                                pic.MoveTo(worksheetTwo.Cell((currentRow ), 1));
                                amountOfPictures += 1;
                            }
                            else
                            {
                                amountOfPictures += 1;
                                //currentRow = currentRow - 11;
                                currentRow = currentRow - 11;
                                pic.MoveTo(worksheetTwo.Cell((currentRow ), 1));

                            }


                            double height = (4.82 * 96) / 2.54;
                            double witdh = (8.57 * 96) / 2.54;

                            pic.Width = (int)Math.Round(witdh);
                            pic.Height = (int)Math.Round(height);

                            // Bitmap bitmapThumb = new Bitmap(thumb,);

                            //var image = worksheetTwo.AddPicture(ImageToByteArray(myImage)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);

                            //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.3).ScaleWidth(0.3);


                            //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, 3)).ScaleHeight(0.3).ScaleWidth(0.3);




                            // worksheetTwo.Column(currentCell).Width = 60;
                            //worksheetTwo.Row(currentRow).Height = image.Height;

                        }
                        catch
                        {

                        }
                    }
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                }

                //foreach (string columnName in columnNames.OrderBy(x => x.ToString()))
                //{
                //    worksheetTwo.Cell(1, currentCell).Value = columnName;
                //    currentCell += 1;
                //}
                //currentCell = 1;
                //foreach (DataRow dr in dt.Rows)
                //{
                //    foreach (string columnName in columnNames.OrderBy(x => x.ToString()))
                //    {
                //        if (columnName.ToLower() == "imageurl")
                //        {
                //            //var imagePath = @"\\************\images\JS020002\94616772-a5fd-4483-c3ad-08d8f788806f-16179618972563280423607572183757.jpg";
                //            if (dr[columnName].ToString() != "")
                //            {
                //                // Write the byte array to the other FileStream.
                //                var imagePath = @"\\************\images\" + dr[columnName].ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();

                //                var image = worksheetTwo.AddPicture(imagePath).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);
                //                worksheetTwo.Column(currentCell).Width = 60;
                //                worksheetTwo.Row(currentRow).Height = image.Height;
                //            }
                //            else
                //            {
                //                worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                //            }


                //        }
                //        else
                //        {
                //            worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                //        }

                //        currentCell += 1;
                //    }
                //    currentCell = 1;
                //    currentRow += 1;

                //}
                //Adjusting size of the excel cell as per requirement 
                //worksheet.Column(3).Width = 60;
                //worksheet.Row(3).Height = 50;



                //Scaling down image as per our cell size


                //Formating the cell with border and color
                //  worksheet.Cell("C2").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                // worksheet.Cell("C2").Style.Border.OutsideBorderColor = XLColor.Blue;

                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                string filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + excelFileData.fileName;
                //send an email now
                try
                {

                    //wb.SaveAs(filenameToSave);
                    //just test logic quick
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + System.DateTime.Now.ToShortDateString().Replace("/", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }
                catch
                {
                    //generate new name
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + System.DateTime.Now.ToShortDateString().Replace("\\", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }


                try
                {
                    //now we can finally send the email.(Actually, we still need to do the managers, but for now lets send it to me).
                    EmailData emailData = new EmailData();
                    //emailData.carbonCopies.Add("<EMAIL>");
                    List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                    ReplacementValues replacement = new ReplacementValues();
                    replacement.StringToFind = "[[FullText]]";
                    replacement.valueToReplaceWith = "Your report is now ready for Download, please click on the link to download it : <a href='" + filenameToSave + " ' download target='_blank'>" + "Download Your Report" + "</a>";
                    replacementValues.Add(replacement);
                    emailData.replacementValuesHtml = replacementValues;
                    //emailData.emailAttachements = new List<string> { filenameToSave };
                    emailData.subject = "Picture Report";
                    emailData.recipient = email;
                    await Task.Run(sendEmail(emailData));
                }
                catch (Exception ex)
                {
                    return ex.Message;
                }




                return null;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }


        [Route("GenerateMerchandisingPictureReportDynamic")]
        [HttpPost]
        public async Task<object> GenerateMerchandisingPictureReportDynamic(GenerateExcelFileData excelFileData)
        {
            try
            {

                //lets have a look at the data
                var json = JsonConvert.SerializeObject(excelFileData.Data);
                var list = JsonConvert.DeserializeObject<List<ReportsController.MerchandisingReportResultsDynamic>>(excelFileData.Data.ToString());

                DataTable dt = (DataTable)JsonConvert.DeserializeObject(json, (typeof(DataTable)));

                XLWorkbook wb = new XLWorkbook();
                //IXLWorksheet worksheet = wb.Worksheets.Add(dt, "Picture");
                IXLWorksheet worksheetTwo = wb.Worksheets.Add("PictureReport");
                int currentRow = 2;
                int currentCell = 1;

                string[] columnNames = dt.Columns.Cast<DataColumn>()
                                     .Select(x => x.ColumnName)
                                     .ToArray();

                //lets create new cells and rows first.
                worksheetTwo.Cell(1, 1).Value = "Picture Report";
                worksheetTwo.Cell(1, 1).Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                ///worksheetTwo.Range("A1:O1").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                //worksheetTwo.Range("A1:O1").Style.Border.OutsideBorderColor = XLColor.Blue;
                //worksheetTwo.Cell(1, 1).Style.Font.FontColor = XLColor.DarkBlue;
                worksheetTwo.Cell(1, 1).Style.Font.FontSize = 16;
                //lets not worry about the color just yet.
                int AmountOfRowsPerRow = 9;
                int amountOfColumns = 2;
                //ok, lets figure it out quickly.
                //merge code below
                //worksheetTwo.Range(worksheet.Cell(row, col++), worksheet.Cell(row, col++)).Row(1).Merge();

                //first, lets create the stuff.

                //for now lets merge 14 columns for testing
                worksheetTwo.Cell(1, 1).Value = "Picture Report";
                //worksheetTwo.Range("A1:O1").Row(1).Merge();

                //we now have the heading, lets do the rest.
                int currentRowToBeOn = 2;
                int firstRowsCount = 3;
                int secondRowCount = 9;
                int thirdRowCount = 1;
                currentCell = 1;

                string currentStore = "";
                string previousStore = "";
                string previousProduct = "";
                string previousDate = "";
                bool isFirstStore = true;
                bool isFirstRun = true;
                int amountOfPictures = 0;
                foreach (var dr in list)
                {
                    int i = 0;

                    if (previousStore == "")
                    {
                        previousStore = dr.Store.ToString();
                        previousDate = dr.ScheduleDate.ToString();
                        previousProduct = dr.Product.ToString();
                        isFirstStore = true;
                    }



                    if (previousStore == dr.Store.ToString() && dr.Product.ToString() == previousProduct && !isFirstRun && dr.ScheduleDate.ToString() == previousDate)
                    {
                        //this is one store data we need to deal with now
                        isFirstStore = false;
                    }
                    else
                    {
                        isFirstStore = true;
                    }
                    previousStore = dr.Store.ToString();
                    previousProduct = dr.Product.ToString();
                    previousDate = dr.ScheduleDate.ToString();
                    isFirstRun = false;

                    //ok, here we need to just add correct values and format a bit more.
                    while (i < firstRowsCount)
                    {

                        if (isFirstStore)
                        {
                            if (i == 0)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Week";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr.ScheduleDate.ToString();
                                //worksheetTwo.Cell(currentRow, 3).Value = "Date Opened";
                                //worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 4).Value = dr["dateOpened"].ToString();
                                //worksheetTwo.Cell(currentRow, 5).Value = "Region";
                                //worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                                //worksheetTwo.Cell(currentRow, 6).Value = dr["Region"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            //we
                            if (i == 1)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Chain";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr.Chain.ToString();
                                //worksheetTwo.Cell(currentRow, 3).Value = "Date Completed";
                                //worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 4).Value = dr["dateClosed"].ToString();
                                //worksheetTwo.Cell(currentRow, 5).Value = "Team";
                                //worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                                //worksheetTwo.Cell(currentRow, 6).Value = dr["Team"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                            if (i == 2)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Store";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr.Store.ToString();
                                //worksheetTwo.Cell(currentRow, 3).Value = "Hours Worked";
                                //worksheetTwo.Cell(currentRow, 3).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 4).Value = "Hours Worked Value";
                                //worksheetTwo.Cell(currentRow, 5).Value = "Date";
                                //worksheetTwo.Cell(currentRow, 5).Style.Font.Bold = true;
                                //worksheetTwo.Cell(currentRow, 5).Style.Fill.BackgroundColor = XLColor.LightGray;
                                //worksheetTwo.Cell(currentRow, 6).Value = dr["scheduleDate"].ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            }
                        }
                        //we

                        currentRow++;
                        i++;
                    }

                    //now we need to create the rest
                    //create an empty row
                    currentRow += 1;
                    //now we can create the rest of the report.
                    //ok, we know the data that is contained, we need to make more rows here
                    //create 3 rows, keep a counter
                    i = 0;
                    int amountOfQuestions = dr.AnswerForReports.Count;
                    bool hasImageValid = true;
                    while (i < secondRowCount)
                    {
                        //we
                        if (i == 0)
                        {
                            if (isFirstStore)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Category";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr.Category.ToString();
                            }
                            //worksheetTwo.Cell(currentRow, 3).Value = "This will be a picture";





                            //worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                        }
                        //we
                        if (isFirstStore)
                        {
                            if (i == 1)
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Media Type";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr.MediaType.ToString();


                            }
                            if(i == 2 && isFirstStore)
                            {
                                foreach (ReportsController.QuestionAndAnswerForReport questionAndAnswerForReport in dr.AnswerForReports)
                                {
                                    
                                    currentRow += 1;
                                    worksheetTwo.Cell(currentRow, 1).Value = questionAndAnswerForReport.Question;
                                    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                    worksheetTwo.Cell(currentRow, 2).Value = questionAndAnswerForReport.Answer.ToString() + " " + questionAndAnswerForReport.Comment;
                                    if (currentRow % 2 == 0)
                                    {
                                        worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                                    }
                                }
                            }
                           
                               
                            //if (i == 2)
                            //{
                            //    worksheetTwo.Cell(currentRow, 1).Value = "Did you pull a “dispo” report in-store?";
                            //    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //    worksheetTwo.Cell(currentRow, 2).Value = dr["isDispo"].ToString();

                            //    worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            //}
                            //if (i == 3)
                            //{
                            //    worksheetTwo.Cell(currentRow, 1).Value = "Are all lines on shelf and neat?";
                            //    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //    worksheetTwo.Cell(currentRow, 2).Value = dr["linesNeat"].ToString();

                            //}
                            //if (i == 4)
                            //{
                            //    worksheetTwo.Cell(currentRow, 1).Value = "Are all lines priced?";
                            //    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //    worksheetTwo.Cell(currentRow, 2).Value = dr["pricedCorrect"].ToString();

                            //    worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            //}
                            //if (i == 5)
                            //{
                            //    worksheetTwo.Cell(currentRow, 1).Value = "Are the stock counts on the system correct?";
                            //    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //    worksheetTwo.Cell(currentRow, 2).Value = dr["stockCounts"].ToString();

                            //}
                            //if (i == 6)
                            //{
                            //    worksheetTwo.Cell(currentRow, 1).Value = "Was there stock in the store room, if so did you pack it onto the shelf?";
                            //    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //    worksheetTwo.Cell(currentRow, 2).Value = dr["stockRoomStock"].ToString();

                            //    worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;
                            //}
                            //if (i == 7)
                            //{
                            //    worksheetTwo.Cell(currentRow, 1).Value = "Did the store manager assist with aged stock?";
                            //    worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //    worksheetTwo.Cell(currentRow, 2).Value = dr["stockAged"].ToString();

                            //}
                            if (i == (amountOfQuestions -3))
                            {
                                worksheetTwo.Cell(currentRow, 1).Value = "Contract";
                                worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                                worksheetTwo.Cell(currentRow, 2).Value = dr.Contract.ToString();

                                worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;

                            }

                        }
                        if (i == amountOfQuestions)
                        {
                            //worksheetTwo.Cell(currentRow, 1).Value = "Picture take on";
                            //worksheetTwo.Cell(currentRow, 1).Style.Font.Bold = true;
                            //worksheetTwo.Cell(currentRow, 2).Value = "Picture take on Value";

                            //worksheetTwo.Range(worksheetTwo.Cell(currentRow, 1), worksheetTwo.Cell(currentRow, 2)).Style.Fill.BackgroundColor = XLColor.LightGray;


                            //worksheetTwo.Range(worksheetTwo.Cell(currentRow - 8, 3), worksheetTwo.Cell(currentRow, 3)).Row(8).Merge();

                            // Write the byte array to the other FileStream.



                        }
                        currentRow++;
                        i++;
                    }
                    i = 0;
                    while (i < thirdRowCount)
                    {
                        i++;
                        try
                        {
                            var imagePath = @"\\************\images\" + dr.ImageURL.ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImagesLQ/?imageURL=\\", "").ToString();

                            //var imagePath = @"\\************\images\" + dr["imageURL"].ToString().Replace("https://localhost:44334/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();


                            Image myImage = Image.FromFile(imagePath, true);


                            Image thumb = myImage.GetThumbnailImage(1280, 720, () => false, IntPtr.Zero);

                            IXLPicture pic = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb));
                            if (isFirstStore)
                            {
                                pic.MoveTo(worksheetTwo.Cell(currentRow, 1));
                                amountOfPictures = 1;
                            }
                            else if (amountOfPictures == 1 && !isFirstStore)
                            {
                                currentRow = currentRow - 11;
                                pic.MoveTo(worksheetTwo.Cell((currentRow), 1));
                                amountOfPictures += 1;
                            }
                            else
                            {
                                amountOfPictures += 1;
                                //currentRow = currentRow - 11;
                                currentRow = currentRow - 11;
                                pic.MoveTo(worksheetTwo.Cell((currentRow), 1));

                            }


                            double height = (4.82 * 96) / 2.54;
                            double witdh = (8.57 * 96) / 2.54;

                            pic.Width = (int)Math.Round(witdh);
                            pic.Height = (int)Math.Round(height);

                            // Bitmap bitmapThumb = new Bitmap(thumb,);

                            //var image = worksheetTwo.AddPicture(ImageToByteArray(myImage)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);

                            //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.3).ScaleWidth(0.3);


                            //var image = worksheetTwo.AddPicture(ImageToByteArrayThumb(thumb)).MoveTo(worksheetTwo.Cell(currentRow, 3)).ScaleHeight(0.3).ScaleWidth(0.3);




                            // worksheetTwo.Column(currentCell).Width = 60;
                            //worksheetTwo.Row(currentRow).Height = image.Height;

                        }
                        catch
                        {

                        }
                    }
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                    currentRow += 1;
                }

                //foreach (string columnName in columnNames.OrderBy(x => x.ToString()))
                //{
                //    worksheetTwo.Cell(1, currentCell).Value = columnName;
                //    currentCell += 1;
                //}
                //currentCell = 1;
                //foreach (DataRow dr in dt.Rows)
                //{
                //    foreach (string columnName in columnNames.OrderBy(x => x.ToString()))
                //    {
                //        if (columnName.ToLower() == "imageurl")
                //        {
                //            //var imagePath = @"\\************\images\JS020002\94616772-a5fd-4483-c3ad-08d8f788806f-16179618972563280423607572183757.jpg";
                //            if (dr[columnName].ToString() != "")
                //            {
                //                // Write the byte array to the other FileStream.
                //                var imagePath = @"\\************\images\" + dr[columnName].ToString().Replace("https://pcaimages.primeinstore.co.za/api/PhotoHanler/GetImages/?imageURL=\\", "").ToString();

                //                var image = worksheetTwo.AddPicture(imagePath).MoveTo(worksheetTwo.Cell(currentRow, currentCell)).ScaleHeight(0.1).ScaleWidth(0.1);
                //                worksheetTwo.Column(currentCell).Width = 60;
                //                worksheetTwo.Row(currentRow).Height = image.Height;
                //            }
                //            else
                //            {
                //                worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                //            }


                //        }
                //        else
                //        {
                //            worksheetTwo.Cell(currentRow, currentCell).Value = dr[columnName].ToString();
                //        }

                //        currentCell += 1;
                //    }
                //    currentCell = 1;
                //    currentRow += 1;

                //}
                //Adjusting size of the excel cell as per requirement 
                //worksheet.Column(3).Width = 60;
                //worksheet.Row(3).Height = 50;



                //Scaling down image as per our cell size


                //Formating the cell with border and color
                //  worksheet.Cell("C2").Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                // worksheet.Cell("C2").Style.Border.OutsideBorderColor = XLColor.Blue;

                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                string filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + excelFileData.fileName;
                //send an email now
                try
                {

                    //wb.SaveAs(filenameToSave);
                    //just test logic quick
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + email.Replace("@primeinstore.co.za", "") + "\\" + System.DateTime.Now.ToShortDateString().Replace("/", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }
                catch
                {
                    //generate new name
                    filenameToSave = pathForExcelFiles + @"\PictureReportsExcelFiles\" + System.DateTime.Now.ToShortDateString().Replace("\\", "-") + excelFileData.fileName;
                    wb.SaveAs(filenameToSave);
                }


                try
                {
                    //now we can finally send the email.(Actually, we still need to do the managers, but for now lets send it to me).
                    EmailData emailData = new EmailData();
                    //emailData.carbonCopies.Add("<EMAIL>");
                    List<ReplacementValues> replacementValues = new List<ReplacementValues>();
                    ReplacementValues replacement = new ReplacementValues();
                    replacement.StringToFind = "[[FullText]]";
                    replacement.valueToReplaceWith = "Your report is now ready for Download, please click on the link to download it : <a href='" + filenameToSave + " ' download target='_blank'>" + "Download Your Report" + "</a>";
                    replacementValues.Add(replacement);
                    emailData.replacementValuesHtml = replacementValues;
                    //emailData.emailAttachements = new List<string> { filenameToSave };
                    emailData.subject = "Picture Report";
                    emailData.recipient = email;
                    await Task.Run(sendEmail(emailData));
                }
                catch (Exception ex)
                {
                    return ex.Message;
                }




                return null;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }


        [Route("CheckNovaContracts")]
        [HttpPost]
        public async Task<object> CheckNovaContracts(string excelFileData)
        {
            List<Burst> lstQualifyingBursts = new List<Burst>();
            //lets have a look at the data
            using (var db = new NovaDBContext())
            {
                lstQualifyingBursts =  db.Bursts.Include(x => x.BurstCategories).Where(x => x.MediaName == "Brand Illuminator: 90 Degrees" || x.MediaName == "Aisle Wing").ToList();
                
            }
            //now we have all the bursts

            foreach(Burst burst in lstQualifyingBursts)
            {
                //see if there are any other in bursts
                List<Burst> lstMatchingBurst = new List<Burst>();
                using (var db = new NovaDBContext())
                {
                    lstMatchingBurst = db.Bursts.Include(x => x.BurstCategories).Where(x => (x.MediaName == "Brand Illuminator: 90 Degrees" || x.MediaName == "Aisle Wing") && x.ChainId == burst.ChainId && burst.BurstCategories.Where(x => x.Priority == 0).FirstOrDefault().CategoryId == x.BurstCategories.Where(x => x.Priority ==0).FirstOrDefault().CategoryId).ToList();

                }
            }


            return null;
        }

        public DataTable ToDataTable<T>(List<T> items)

        {

            DataTable dataTable = new DataTable(typeof(T).Name);

            //Get all the properties

            PropertyInfo[] Props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (PropertyInfo prop in Props)

            {

                //Setting column names as Property names

                dataTable.Columns.Add(prop.Name);

            }

            foreach (T item in items)

            {

                var values = new object[Props.Length];

                for (int i = 0; i < Props.Length; i++)

                {

                    //inserting property values to datatable rows

                    values[i] = Props[i].GetValue(item, null);

                }

                dataTable.Rows.Add(values);

            }

            //put a breakpoint here and check datatable

            return dataTable;

        }
        public class MailServer
        {
            public string MailServerID { get; set; }
            public string HostName { get; set; }
            public int Port { get; set; }
            public string Ssl { get; set; }
            public string Username { get; set; }
            public string Password { get; set; }
        }
        public class EmailData
        {
            public EmailData()
            {
                sender = "<EMAIL>";
                emailAttachements = new List<string>();
                replacementValuesHtml = new List<ReplacementValues>();
                PDFAttachmentDetails = new List<PDFAttachmentDetails>();
                 htmlTemplate = @"\\************\API Resources\Resources\EmailTemplates\SummaryPDFTemplate.html";
                carbonCopies = new List<string>();


            }
            public string recipient { get; set; }
            public string sender { get; set; }
            public List<string> carbonCopies { get; set; }
            public string subject { get; set; }
            public string htmlToSend { get; set; }

            public string textToSend { get; set; }

            public List<string> emailAttachements { get; set; }

            public List<ReplacementValues> replacementValuesHtml { get; set; }

            public List<PDFAttachmentDetails> PDFAttachmentDetails { get; set; }

            public string htmlTemplate { get; set; }

           

        }

        public class ReplacementValues
        {
            public string StringToFind { get; set; }
            public string valueToReplaceWith { get; set; }
        }

        public class PDFAttachmentDetails
        {
            public PDFAttachmentDetails()
            {
                replacementValuesPDF = new List<ReplacementValues>();
                pdfTemplate = "";
            }
            public string pdfTemplate { get; set; }

            public string pdfName { get; set; }

            public string pdfFolder { get; set; }
            public List<ReplacementValues> replacementValuesPDF { get; set; }
        }


        public class TestImpActions
        {
            public string Action { get; set; }
            public string Category { get; set; }
        }

        //this is for the importation of granite

        public class StockSummary
        {
            public string Code { get; set; }
            public int qty { get; set; }
            public string Location { get; set; }
            public string Item { get; set; }

            public string Barcode { get; set; }
            public int MasterItemId { get; set; }

            public int StoreId { get; set; }

            public int InstallationTeamId { get; set; }
            public int WarehouseId { get; set; }

        }

        public class UsersImported
        {
            public string FirstName { get; set; }
            public string Surname { get; set; }
            public string TeamName { get; set; }
            public string CellNumber { get; set; }
            public string EmailAddress { get; set; }
        }

        public class GenerateExcelFileData
        {
            public GenerateExcelFileData()
            {
                includePicture = true;
                saveAsAttachment = false;
                Subject = "";
            }

            public string emailToSendTo { get; set; }
            public string fileName { get; set; }
            public bool includePicture { get; set; }
            public bool saveAsAttachment { get; set; }

            public string Subject { get; set; }
            public Object Data { get; set; }
        }

        public class baseFilesToUpload
        {
            public string baseImage { get; set; }
            public string actionID { get; set; }

            public int id { get; set; }

          
        }

    }


  
}
