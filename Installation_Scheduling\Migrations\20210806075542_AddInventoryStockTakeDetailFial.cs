﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddInventoryStockTakeDetailFial : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "InventoryStockTakeDetails",
                columns: table => new
                {
                    InventoryStockTakeDetailId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    InventoryStockTakeId = table.Column<int>(type: "int", nullable: false),
                    WarehouseId = table.Column<int>(type: "int", nullable: true),
                    InstallationTeamId = table.Column<int>(type: "int", nullable: true),
                    InventoryStockTakeRoleId = table.Column<int>(type: "int", nullable: false),
                    userName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    isCompleted = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InventoryStockTakeDetails", x => x.InventoryStockTakeDetailId);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetails_InstallationTeam_InstallationTeamId",
                        column: x => x.InstallationTeamId,
                        principalSchema: "Ops",
                        principalTable: "InstallationTeam",
                        principalColumn: "InstallationTeamID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetails_InventoryStockTake_InventoryStockTakeId",
                        column: x => x.InventoryStockTakeId,
                        principalSchema: "Ops",
                        principalTable: "InventoryStockTake",
                        principalColumn: "InventoryStockTakeId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetails_InventoryStockTakeRole_InventoryStockTakeRoleId",
                        column: x => x.InventoryStockTakeRoleId,
                        principalSchema: "Ops",
                        principalTable: "InventoryStockTakeRole",
                        principalColumn: "InventoryStockTakeRoleId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_InventoryStockTakeDetails_Warehouse_WarehouseId",
                        column: x => x.WarehouseId,
                        principalSchema: "Store",
                        principalTable: "Warehouse",
                        principalColumn: "WarehouseID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetails_InstallationTeamId",
                table: "InventoryStockTakeDetails",
                column: "InstallationTeamId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetails_InventoryStockTakeId",
                table: "InventoryStockTakeDetails",
                column: "InventoryStockTakeId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetails_InventoryStockTakeRoleId",
                table: "InventoryStockTakeDetails",
                column: "InventoryStockTakeRoleId");

            migrationBuilder.CreateIndex(
                name: "IX_InventoryStockTakeDetails_WarehouseId",
                table: "InventoryStockTakeDetails",
                column: "WarehouseId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InventoryStockTakeDetails");
        }
    }
}
