﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models;
using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Interfaces;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.CSharp.RuntimeBinder;
using System.Data;
using System.Reflection;
using System.Drawing;
using System.IO;
using System.Drawing.Imaging;
using System.Reflection.Metadata;
using System.Globalization;
using PhoenixAPI.Services.EmailModel;
using PhoenixAPI.Services.Interfaces;
using System.Text.Json;
using PhoenixAPI.Models.IncomingMedia;
using PhoenixAPI.Helpers.IRCodesHelper;

namespace PhoenixAPI.Controllers
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Route("api/[controller]")]
    [Authorize(Roles = "Ad<PERSON>,<PERSON>OpsUser,<PERSON>man,MyMobilityUser")]
    //[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "Admin")]
    public class OpsManagementController : ControllerBase
    {
        private static string pathForImages = @"\\192.168.0.16\Images\Media-Monitor\";
        private readonly IEmailService _emailService;
        private readonly IStockTakeService _stockTakeService;
        private readonly IMediaMonitorService _mediaMonitorService;
        private readonly NovaDBContext _context;

        public OpsManagementController(IEmailService emailService, NovaDBContext context, IStockTakeService stockTakeService, IMediaMonitorService mediaMonitorService)
        {
            _context = context;
            _emailService = emailService;
            _mediaMonitorService = mediaMonitorService;
            _stockTakeService = stockTakeService;
        }

        public OpsManagementController()
        {
        }

        #region TeamManagement
        #region GetMethods
        [Route("GetTeams")]
        [HttpGet]
        public IEnumerable<InstallationTeam> GetTeams()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                return context.InstallationTeam
                    .ToList().OrderBy(x => x.InstallationTeamName);
            }

        }

        [Route("GetTeamsByStoreman")]
        [HttpGet]
        public IEnumerable<InstallationTeam> GetTeamsByStoreman(string storeman)
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {
                return context.InstallationTeam.Where(x => x.InstallationTeamUser != null)
                    .ToList().OrderBy(x => x.InstallationTeamName);
            }

        }


        [Route("GetTeamStores/{teamId}")]
        [HttpGet]
        public IEnumerable<Store> GetTeamStores(int teamId)
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {

                try
                {
                    var returnVal = context.Store
                     .Include(InstallationDays => InstallationDays.InstallationDaysInstallationDay)
                     .Include(a => a.Region).ThenInclude(c => c.Chain)

                     .Where(x => x.InstallationTeamId == teamId)
                     .ToList().OrderBy(x => x.StoreName);
                    return returnVal;
                }
                catch (Exception ex)
                {
                    string error = ex.Message;
                    return null;
                }


            }

        }
        #endregion
        #endregion

        [AllowAnonymous]
        [Route("GetImageById")]
        [HttpGet]
        public async Task<IActionResult> GetImageById(Guid contractId)
        {
            string[] files = Directory.GetFiles(pathForImages);

            var result = await _context.MediaMonitors.Where(x => x.ContractId == contractId).FirstOrDefaultAsync();

            var file = files.FirstOrDefault(x => x == result.ImagePath);

            if (result == null)
            {
                return NotFound("Not Found!");
            }

            if (string.IsNullOrEmpty(file))
            {
                return NotFound("Image Path Not Found!");
            }

            byte[] newFile = System.IO.File.ReadAllBytes(file);
            return File(newFile, "image/jpeg");
        }


        [Route("sendEmailToRequestMedia")]
        [HttpPost]
        public async Task<IActionResult> sendEmailToRequestMedia(MediaRequisitionRequest model)
        {
            var result = await _emailService.SendAsync(model).ConfigureAwait(false);

            return Ok(result);
        }

        [Route("GetImagePaths")]
        [HttpGet]
        public IActionResult GetImagePaths()
        {
            string[] files = Directory.GetFiles(pathForImages);

            return Ok(files);
        }

        [Route("GetMediaMonitorRegions")]
        [HttpGet]
        public async Task<object> GetMediaMonitorRegions()
        {
            var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

            switch (email)
            {
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Eastern Cape" && s.RegionName == "Garden Route" && s.RegionName == "Port Elizabeth").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Free State").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Gauteng").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Gauteng").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "KwaZulu Natal").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Limpopo").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Limpopo 02").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Mpumalanga").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Northern Natal" && s.RegionName == "Northern Natal 2").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "Western Cape").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "National Warehouse").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "National Warehouse").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.Where(s => s.RegionName == "National Warehouse").ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.ToListAsync();
                case "<EMAIL>":
                    return await _context.OpsMediaMonitorRegions.ToListAsync();
                default:
                    return new List<object>();
            }
        }

        [Route("GetMediaForQuestionsAndAnwers")]
        [HttpGet]
        public async Task<object> GetMediaForQuestionsAndAnwers()
        {
            using (var db = new NovaDBContext())
            {

                //this gets all media and removes media where there are already questions
                var results = await db.Media.Include(x => x.Questions).ToListAsync();

                return results.Where(x => x.Questions.Count == 0).OrderBy(x => x.MediaName).ToList();
            }
        }
        [Route("GetExistingQuestionsAndAnswers")]
        [HttpGet]
        public async Task<object> GetExistingQuestionsAndAnswers()
        {
            using (var db = new NovaDBContext())
            {
                var resultsa = db.Media.Include(x => x.Questions).ToList();
                //this gets all media and removes media where there are already questions
                var results = resultsa.Where(x => x.Questions.Count > 0);

                return results;
            }
        }

        [Route("GetStockTakeDetails")]
        [HttpGet]
        public async Task<object> GetStockTakeDetails()
        {
            //check if there is a stock take in progress
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryStockTakeDetails.Include(x => x.InventoryStockTakeDetailsBarcodes).ThenInclude(x => x.MasterItem).Include(x => x.InstallationTeam).Include(x => x.Warehouse).Include(x => x.InventoryStockTakeRole).Where(x => x.isCompleted == false && x.userName == email).FirstOrDefaultAsync();


                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }


        [Route("GetInstallationDays")]
        [HttpGet]
        public IEnumerable<InstallationDays> GetInstallationDays()
        {
            //Querying with LINQ to Entities 
            using (var context = new NovaDBContext())
            {




                return context.InstallationDays
                .ToList().OrderBy(x => x.InstallationDayId);
            }

        }

        [Route("UpdateMasterItemInventoryItem")]
        [HttpPost]
        public async Task<object> UpdateMasterItemInventoryItem(MasterItemInventoryItem masterItem)
        {
            using (var db = new NovaDBContext())
            {
                var result = await db.MasterItemInventoryItems.Where(s => s.Id == masterItem.Id).FirstOrDefaultAsync();

                result.MasterItemId = masterItem.MasterItemId;
                db.MasterItemInventoryItems.Update(result);
                db.SaveChanges();

                return result;
            }
        }

        [Route("UpdateInventoryStockTakeDetailsBarcodes")]
        [HttpPost]
        public async Task<object> UpdateInventoryStockTakeDetailsBarcodes(InventoryStockTakeDetailsMasterItem item)
        {
            var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

            using (var db = new NovaDBContext())
            {
                var result = await db.InventoryStockTakeDetailsBarcodes.Where(s => s.InventoryStockTakeDetailsBarcodesId == item.inventoryStockTakeDetailsBarcodesId && s.InventoryStockTakeDetailId == item.id
                && s.InventoryStockTakeDetails.InventoryStockTakeId == item.stockTakeId && s.userName == email).FirstOrDefaultAsync();

                if (result != null)
                {
                    result.MasterItemId = item.masterItemId;
                    result.isWrongCapex = item.isWrongCapex;
                    db.InventoryStockTakeDetailsBarcodes.Update(result);
                    db.SaveChanges();
                }

                return result;
            }
        }

        [Route("UpdateStore")]
        [HttpPost]
        public void UpdateStore(Store store)
        {
            //Querying with LINQ to Entities 
            //here we need to update the store with the new details
            int test = store.InstallationDaysInstallationDay.InstallationDayId;

            using (var db = new NovaDBContext())
            {
                var result = db.Store.SingleOrDefault(s => s.StoreId == store.StoreId);
                if (result != null)
                {
                    result.InstallationDaysInstallationDay = store.InstallationDaysInstallationDay;
                    if (store.InstallationTeam.InstallationTeamId != 0)
                    {
                        result.InstallationTeamId = store.InstallationTeam.InstallationTeamId;

                        var allInstallationsToUpdate = db.InstallationScheduleCurrent.Where(x => x.StoId == store.StoreId && x.IsCurrent == true).ToList();

                        foreach (InstallationScheduleCurrent installation in allInstallationsToUpdate)
                        {
                            installation.Installationteamid = store.InstallationTeam.InstallationTeamId;
                            db.Update(installation);
                        }
                    }

                    db.SaveChanges();
                }
            }
        }

        [Route("GetStore")]
        [HttpGet]
        public IActionResult GetStore(int StoreId)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var result = db.Store.Include(s => s.Region).FirstOrDefault(s => s.StoreId == StoreId);

                    return Ok(new
                    {
                        StoreId = result.StoreId,
                        RegionId = result.RegionID,
                        RegionName = result.Region.RegionName
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("GetStorePerRegion")]
        [HttpGet]
        public IActionResult GetStorePerRegion()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var result = db.Regions.Include(s => s.Stores).ToList();

                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("GetRegionName")]
        [HttpGet]
        public string GetRegionName(int StoreId)
        {
            using (var db = new NovaDBContext())
            {
                var result = db.Store.Include(s => s.Region).FirstOrDefault(s => s.StoreId == StoreId);

                if (result == null)
                {
                    return "";
                }

                return result.Region.RegionName;
            }
            ;
        }


        public Store Add(Store store)
        {
            throw new NotImplementedException();
        }

        public Store Update(Store store)
        {
            throw new NotImplementedException();
        }

        public Store Delete(Store store)
        {
            throw new NotImplementedException();
        }


        //IR Codes
        [Route("AddIRCode")]
        [HttpPost]
        public async Task<object> AddIRCode(IRCodes iRCodes)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    iRCodes.CreatedBy = email;
                    db.IRCodes.Add(iRCodes);
                    db.SaveChanges();
                    var result = await db.IRCodes.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }
        [Route("UpdateIRCode")]
        [HttpPost]
        public async Task<object> UpdateIRCode(IRCodes iRCodes)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    var result = db.IRCodes.SingleOrDefault(s => s.IRCodeID == iRCodes.IRCodeID);
                    result.IRCodeName = iRCodes.IRCodeName;
                    result.Dormant = iRCodes.Dormant;
                    result.hasComment = iRCodes.hasComment;
                    result.DefaultComment = iRCodes.DefaultComment;
                    result.countsForStrikeRate = iRCodes.countsForStrikeRate;
                    result.forceCapexScan = iRCodes.forceCapexScan;
                    result.needsPicture = iRCodes.needsPicture;
                    result.isStoreSkip = iRCodes.isStoreSkip;
                    db.SaveChanges();
                    var returnResult = await db.IRCodes.ToListAsync();
                    return returnResult;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }

        }
        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser")]
        [Route("GetIRCodes")]
        [HttpGet]
        public async Task<object> GetIRCodes()
        {
            try
            {
                using (var db = new NovaDBContext())
                {

                    var result = await db.IRCodes.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }



        //we need to add schedule generations here
        [Authorize(Roles = "ScheduleGenerator")]
        [Route("ArchiveSchedule")]
        [HttpPost]
        public async Task<object> ArchiveSchedule(DateTime ShceduleDate)
        {
            try
            {


                // var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {


                    List<InstallationScheduleCurrent> lstSchedule = new List<InstallationScheduleCurrent>();
                    lstSchedule = db.InstallationScheduleCurrent.Where(x => x.ForDate == "Apr 05, 2021").ToList();



                    List<InstallationScheduleArchived> installationScheduleArchiveds = new List<InstallationScheduleArchived>();



                    foreach (InstallationScheduleCurrent ins in lstSchedule)
                    {
                        InstallationScheduleArchived archived = new InstallationScheduleArchived();

                        PropertyInfo[] properties = typeof(InstallationScheduleCurrent).GetProperties();
                        PropertyInfo[] propertyInfoArchived = typeof(InstallationScheduleArchived).GetProperties();


                        foreach (PropertyInfo property in properties)
                        {
                            foreach (PropertyInfo propArchived in propertyInfoArchived)
                            {
                                if (property.Name == propArchived.Name)
                                {
                                    try
                                    {
                                        var value = property.GetValue(ins);
                                        if (value != null)
                                        {
                                            propArchived.SetValue(archived, value);
                                        }
                                    }
                                    catch
                                    {

                                    }


                                }

                            }
                            //property.SetValue(archived, property.GetValue(ins));
                        }

                        installationScheduleArchiveds.Add(archived);
                    }

                    try
                    {
                        db.InstallationScheduleArchived.AddRange(installationScheduleArchiveds);
                        db.SaveChanges();
                        db.InstallationScheduleCurrent.RemoveRange(lstSchedule);
                        db.SaveChanges();
                    }
                    catch
                    {

                    }







                    return true;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        //we need to add schedule generations here
        //[Authorize(Roles = "ScheduleGenerator")]
        [AllowAnonymous]
        [Route("GenerateSchedule")]
        [HttpPost]
        public async Task<object> GenerateSchedule()
        {



            DateTime ShceduleDate = new DateTime(2023, 01, 09, 09, 15, 00);//

            var context = new NovaDBContext();

            //DID THIS BECAUSE WITH REPORTS AND AND 
            List<IRCodes> _list = new List<IRCodes>();
            var _irc = IRC.GetInstance();
            _list = _irc.getList();
            if (_list.Count == 0)
            {
                _list = context.IRCodes.ToList();
                _irc.setList(_list);
            }
            try
            {
                //var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                string email = "<EMAIL>";
                // var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {


                    db.Database.ExecuteSqlRaw("Update [Ops].[InstallationScheduleCurrent] set IsCurrent = 0 ");
                    InstallationScheduleDates installationScheduleDates = new InstallationScheduleDates();
                    installationScheduleDates.isLatest = true;
                    installationScheduleDates.isCurrent = true;
                    installationScheduleDates.CreatedBy = email;
                    installationScheduleDates.ScheduleDate = ShceduleDate;

                    //lets see if the date exists
                    var getScheduleDates = db.InstallationScheduleDates.Where(x => x.ScheduleDate != ShceduleDate).ToList();
                    if (getScheduleDates.Count > 0)
                    {
                        //we need to update them all here
                        foreach (InstallationScheduleDates scheduleDates in getScheduleDates)
                        {
                            scheduleDates.isCurrent = false;
                            scheduleDates.isLatest = false;
                            db.Update(scheduleDates);
                        }
                    }

                    db.InstallationScheduleDates.Add(installationScheduleDates);
                    db.SaveChanges();


                    //now we need to get the rest of them, and then insert into this table
                    var results = db.VMyMobilityExportWithDays.ToList().OrderBy(x => x.Store);

                    var targetList = results.Select(x => new InstallationScheduleCurrent()
                    {
                        InstallationScheduleCurrentID = new System.Guid()
                        ,
                        CampaignFinished = false
                        ,
                        CampaignIRCodeSelected = false
                        ,
                        CampaignPicturePath = ""
                        ,
                        CampaignPictureTaken = false
                        ,
                        CampaignSpecialInstructionsRead = false
                        ,
                        CategoryName = x.CategoryName
                        ,
                        Chain = x.Chain
                        ,
                        Client = x.Client
                        ,
                        CommencementDate = x.CommencementDate
                        ,
                        CreatedBy = x.CreatedBy
                        ,
                        Cycle = x.Cycle
                        ,
                        DayOfCommencementDate = x.DayOfCommencementDate
                        ,
                        DayOfLastModified = x.DayOfLastModified
                        ,
                        DayOfTerminationDate = x.DayOfTerminationDate
                        ,
                        ForDate = x.ForDate
                        ,
                        GeneratedScheduleDate = System.DateTime.Now
                        ,
                        Group = x.Group
                        ,
                        InstallationDay = x.InstallationDay
                        ,
                        IRCodeID = 56
                        ,
                        JobNumber = x.JobNumber
                        ,
                        LastModified = x.LastModified
                        ,
                        LastModifiedBy = x.LastModifiedBy
                        ,
                        MediaType = x.MediaType
                        ,
                        MonthOfCommencementDate = x.MonthOfCommencementDate
                        ,
                        MonthOfLastModified = x.MonthOfLastModified
                        ,
                        MonthOfTerminationDate = x.MonthOfTerminationDate
                        ,
                        NumberOfWeeks = x.NumberOfWeeks
                        ,
                        Product = x.Product
                        ,
                        QtyToInstall = x.QtyToInstall
                        ,
                        Quantity = x.Quantity
                        ,
                        QuarterOfCommencementDate = x.QuarterOfCommencementDate
                        ,
                        QuarterOfLastModified = x.QuarterOfLastModified
                        ,
                        QuarterOfTerminationDate = x.QuarterOfTerminationDate
                        ,
                        Region = x.Region
                        ,
                        Section = x.Section
                        ,
                        SelectedIRCode = null
                        ,
                        SpecialInstructions = x.SpecialInstructions
                        ,
                        Status = x.Status
                        ,
                        StoId = x.StoId

                        ,
                        Store = x.Store
                        ,
                        StoreAndChain = x.StoreAndChain
                        ,
                        Teamlist = x.Teamlist
                        ,
                        TerminationDate = x.TerminationDate
                        ,
                        WeekOfCommencementDate = x.WeekOfCommencementDate
                        ,
                        WeekOfLastModified = x.WeekOfLastModified
                        ,
                        WeekOfTerminationDate = x.WeekOfTerminationDate
                        ,
                        YearOfCommencementDate = x.YearOfCommencementDate
                        ,
                        YearOfLastModified = x.YearOfLastModified
                        ,
                        AllowPictureAfterwards = true,

                        YearOfTerminationDate = x.YearOfTerminationDate
                        ,
                        MediaId = x.MediaId
                        ,
                        Installationteamid = x.Installationteamid
                        ,
                        BurstID = x.BurstID
                        ,
                        IsCurrent = true
                    }).ToList();

                    //List<InstallationScheduleCurrent> targetList = new List<InstallationScheduleCurrent>(results.Cast<InstallationScheduleCurrent>());
                    db.InstallationScheduleCurrent.AddRange(targetList);
                    db.SaveChanges();

                    try
                    {
                        //we need to load previous IRCodes here
                        var CurrentList = db.InstallationScheduleCurrent.Where(x => x.IsCurrent == true && (x.Status == "Running" || x.Status == "Remove")).ToList();
                        var previousDate = db.InstallationScheduleCurrent.Where(x => x.IsCurrent == false).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                        var PreviousList = db.InstallationScheduleCurrent.Where(x => x.ForDate == previousDate.ForDate && (x.Status == "Running" || x.Status == "Install")).ToList();

                        List<InstallationScheduleCurrent> lstFinal = new List<InstallationScheduleCurrent>();

                        Parallel.ForEach(targetList, ins =>
                        {
                            //here we need to conver the rows
                            var previousInstallation = PreviousList.Where(x => x.IsCurrent == false &&
                            //x.Status != "Install" &&
                            x.JobNumber == ins.JobNumber &&
                            x.MediaType == ins.MediaType &&
                            x.CategoryName == ins.CategoryName &&
                            x.StoId == ins.StoId && x.Product == ins.Product &&
                            x.Status != "Install" &&
                            x.Client == ins.Client).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                            if (previousInstallation != null)
                            {
                                //we can set it
                                //lest see if it gets here.
                                ins.PreviousIRCodeId = previousInstallation.IRCodeID;
                                ins.PreviousIRCode = _list.FirstOrDefault(p => p.IRCodeID == previousInstallation.IRCodeID);// previousInstallation.SelectedIRCode;
                                ins.PreviousIRCodeComment = previousInstallation.IRCodeComment;
                                lstFinal.Add(ins);


                            }
                            else
                            {
                                ins.PreviousIRCode = null;
                            }
                        });

                        db.UpdateRange(lstFinal);
                        db.SaveChanges();


                    }
                    catch (Exception ex)
                    {

                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }
        [Route("RegenetateSchudyle")]
        [HttpPost]
        public async Task<object> RegenetateSchudyle()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    //we need to load previous IRCodes here
                    //2022-07-02 17:53:25.543
                    //DateTime date = new DateTime(2022, 07, 2, 17, 53);

                    string iDate = "2022-07-02 00:00:00.0000";
                    DateTime oDate = Convert.ToDateTime(iDate);


                    string emdate = "2022-07-03 23:59:00.0000";
                    DateTime enddate = Convert.ToDateTime(emdate);

                    var CurrentList = db.InstallationScheduleCurrent.Where(x => x.IsCurrent == true && (x.Status == "Running" || x.Status == "Remove")).ToList();
                    var previousDate = db.InstallationScheduleCurrent.Where(x => x.IsCurrent == false && x.GeneratedScheduleDate >= oDate && x.GeneratedScheduleDate <= enddate).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();


                    var PreviousList = db.InstallationScheduleCurrent.Where(x => x.ForDate == previousDate.ForDate && (x.Status == "Running" || x.Status == "Install" && x.GeneratedScheduleDate >= oDate && x.GeneratedScheduleDate <= enddate) && x.Chain != "Phoenix Test Data" && x.CampaignFinished == true).ToList();

                    List<InstallationScheduleCurrent> lstFinal = new List<InstallationScheduleCurrent>();

                    //foreach(InstallationScheduleCurrent installationScheduleCurrent in CurrentList)
                    //{
                    //    var previousInstallation = PreviousList.Where(x => x.IsCurrent == false && x.JobNumber == installationScheduleCurrent.JobNumber && x.MediaType == installationScheduleCurrent.MediaType && x.CategoryName == installationScheduleCurrent.CategoryName && x.StoId == installationScheduleCurrent.StoId && x.Product == installationScheduleCurrent.Product && x.Client == installationScheduleCurrent.Client && x.GeneratedScheduleDate >= oDate && x.GeneratedScheduleDate <= enddate).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                    //    installationScheduleCurrent.PreviousIRCodeId = previousInstallation.IRCodeID;
                    //    installationScheduleCurrent.PreviousIRCode = previousInstallation.SelectedIRCode;
                    //    installationScheduleCurrent.PreviousIRCodeComment = previousInstallation.IRCodeComment;
                    //    db.Update(installationScheduleCurrent);
                    //    db.SaveChanges();
                    //}

                    //Parallel.ForEach(CurrentList, installationScheduleCurrent =>
                    //{
                    //    var previousInstallation = 
                    //    PreviousList.Where(x => x.IsCurrent == false 
                    //    && x.JobNumber == installationScheduleCurrent.JobNumber 
                    //    && x.MediaType == installationScheduleCurrent.MediaType 
                    //    && x.CategoryName == installationScheduleCurrent.CategoryName 
                    //    && x.StoId == installationScheduleCurrent.StoId 
                    //    && x.Product == installationScheduleCurrent.Product 
                    //    && x.Client == installationScheduleCurrent.Client 
                    //    && x.GeneratedScheduleDate >= oDate 
                    //    && x.GeneratedScheduleDate <= enddate).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                    //    installationScheduleCurrent.PreviousIRCodeId = previousInstallation.IRCodeID;
                    //    installationScheduleCurrent.PreviousIRCode = previousInstallation.SelectedIRCode;
                    //    installationScheduleCurrent.PreviousIRCodeComment = previousInstallation.IRCodeComment;         
                    //});


                    Parallel.ForEach(CurrentList, installationScheduleCurrent =>
                    {
                        //here we need to conver the rows
                        //var previousInstallation = PreviousList.Where(x => x.IsCurrent == false && x.JobNumber == installationScheduleCurrent.JobNumber && x.MediaType == installationScheduleCurrent.MediaType && x.CategoryName == installationScheduleCurrent.CategoryName && x.StoId == installationScheduleCurrent.StoId && x.Product == installationScheduleCurrent.Product && x.Client == installationScheduleCurrent.Client && x.GeneratedScheduleDate >= oDate && x.GeneratedScheduleDate <= enddate).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();

                        var previousInstallation = PreviousList.Where(x => x.IsCurrent == false && x.JobNumber == installationScheduleCurrent.JobNumber && x.MediaType == installationScheduleCurrent.MediaType && x.CategoryName == installationScheduleCurrent.CategoryName && x.StoId == installationScheduleCurrent.StoId && x.Product == installationScheduleCurrent.Product && x.Client == installationScheduleCurrent.Client && x.GeneratedScheduleDate >= oDate && x.GeneratedScheduleDate <= enddate).OrderByDescending(x => x.GeneratedScheduleDate).FirstOrDefault();
                        if (previousInstallation != null)
                        {
                            //we can set it
                            //lest see if it gets here.
                            installationScheduleCurrent.PreviousIRCodeId = previousInstallation.IRCodeID;
                            installationScheduleCurrent.PreviousIRCode = previousInstallation.SelectedIRCode;
                            installationScheduleCurrent.PreviousIRCodeComment = previousInstallation.IRCodeComment;
                            lstFinal.Add(installationScheduleCurrent);


                        }
                    });

                    db.UpdateRange(lstFinal);
                    db.SaveChanges();
                }


            }
            catch (Exception ex)
            {

            }
            return null;
        }


        [Route("GetScheduleDates")]
        [HttpGet]
        public async Task<object> GetScheduleDates()
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    var results = db.InstallationScheduleDates.Where(x => x.isCurrent == true).FirstOrDefault();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Route("GetLocationCategories")]
        [HttpGet]
        public async Task<object> GetLocationCategories()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = db.Chain.ToList().Where(x => x.Dormant == false).OrderBy(x => x.ChainName);
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetLocationTypes")]
        [HttpGet]
        public async Task<object> GetLocationTypes()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = db.LocationTypes.ToList().OrderBy(x => x.LocationTypeId);
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("AddLocationType")]
        [HttpPost]
        public async Task<object> AddLocationType(LocationType locationType)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    locationType.CreatedBy = email;
                    db.LocationTypes.Add(locationType);
                    db.SaveChanges();
                    var result = await db.LocationTypes.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }
        [Authorize(Roles = "Admin,NovaOpsUser,MyMobilityUser,Storeman")]
        [Route("GetRegions")]
        [HttpGet]
        public async Task<object> GetRegions()
        {
            using (var context = new NovaDBContext())
            {
                //var results = await context.OpsRegions.FromSqlRaw("select * from ops.Region").ToListAsync();
                var results = await context.OpsRegions.ToListAsync();
                return results;
            }
        }

        [Route("AddRegion")]
        [HttpPost]
        public async Task<object> AddRegion(OpsRegion region)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    region.CreatedBy = email;
                    db.OpsRegions.Add(region);
                    db.SaveChanges();
                    var result = await db.OpsRegions.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("UpdateRegion")]
        [HttpPost]
        public async Task<object> UpdateRegion(OpsRegion region)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var result = db.OpsRegions.SingleOrDefault(s => s.RegionId == region.RegionId);
                    result.RegionName = region.RegionName;
                    result.RegionCode = region.RegionCode;
                    result.Dormant = region.Dormant;
                    db.SaveChanges();
                    var returnResult = await db.OpsRegions.ToListAsync();
                    return returnResult;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("UpdateLocationType")]
        [HttpPost]
        public async Task<object> UpdateLocationType(LocationType locationType)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    var result = db.LocationTypes.SingleOrDefault(s => s.LocationTypeId == locationType.LocationTypeId);
                    result.LocationTypeName = locationType.LocationTypeName;
                    result.LocationTypeDescription = locationType.LocationTypeDescription;
                    result.Dormant = locationType.Dormant;
                    db.SaveChanges();
                    var returnResult = await db.LocationTypes.ToListAsync();
                    return returnResult;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }

        }

        //master item types
        [Route("GetMasterItemTypes")]
        [HttpGet]
        public async Task<object> GetMasterItemTypes()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = db.MasterItemTypes.ToList().OrderBy(x => x.MasterItemTypeId);
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("AddMasterItemType")]
        [HttpPost]
        public async Task<object> AddMasterItemType(MasterItemType masterItemType)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    masterItemType.CreatedBy = email;
                    db.MasterItemTypes.Add(masterItemType);
                    db.SaveChanges();
                    var result = await db.MasterItemTypes.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("AddMemberToTeam")]
        [HttpPost]
        public async Task<object> AddMemberToTeam(TeamManager teamManagerItem)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    teamManagerItem.ApplicationUserId = email;

                    //lets look for it first
                    var hasResult = db.TeamManagers.Where(x => x.InstallationTeamId == teamManagerItem.InstallationTeamId).FirstOrDefault();

                    if (hasResult != null)
                    {
                        hasResult.TeamManagerId = teamManagerItem.TeamManagerId;
                        db.Update(hasResult);
                        db.SaveChanges();
                    }
                    else
                    {
                        db.TeamManagers.Add(teamManagerItem);
                        db.SaveChanges();
                    }



                    var result = await db.TeamManagers.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("UpdateTeamMember")]
        [HttpPost]
        public async Task<object> UpdateTeamMember(TeamManager teamManagerItem)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    var result = db.TeamManagers.SingleOrDefault(s => s.InstallationTeamId == teamManagerItem.InstallationTeamId);
                    //teamManagerItem.ApplicationUserId = email;
                    if (result == null)
                    {
                        //it is a new one
                        db.TeamManagers.Add(teamManagerItem);
                        db.SaveChanges();
                    }
                    else
                    {


                        result.ApplicationUserId = teamManagerItem.ApplicationUserId;
                        db.SaveChanges();
                    }

                    var returnResult = await db.TeamManagers.ToListAsync();
                    return returnResult;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("UpdateTeamUser")]
        [HttpPost]
        public async Task<object> UpdateTeamUser(InstallationTeam installationTeam)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    var result = db.InstallationTeam.SingleOrDefault(s => s.InstallationTeamId == installationTeam.InstallationTeamId);
                    result.InstallationTeamUser = installationTeam.InstallationTeamUser;
                    db.SaveChanges();

                    var returnResult = await db.TeamManagers.ToListAsync();
                    return returnResult;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("UpdateTeamRegion")]
        [HttpPost]
        public async Task<object> UpdateTeamRegion(InstallationTeam installationTeam)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    var result = db.InstallationTeam.SingleOrDefault(s => s.InstallationTeamId == installationTeam.InstallationTeamId);

                    result.RegionId = installationTeam.RegionId;
                    db.SaveChanges();

                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("GetTeamsAndManagers")]
        [HttpGet]
        public async Task<object> GetTeamsAndManagers()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var result = db.InstallationTeam.Include(x => x.TeamManagers).Include(x => x.Region).OrderBy(x => x.InstallationTeamName).ToList();

                    var returnResult = await db.TeamManagers.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        ///master item logic
        ///i need to put stuff into regions
         //master item types
        [Route("GetMasterItems")]
        [HttpGet]
        public async Task<object> GetMasterItems()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = db.MasterItems.Include(x => x.MasterItemType).Include(y => y.MasterItemGroupMembers).ThenInclude(z => z.MasterItemGroup).ToList().OrderBy(x => x.MasterItemName);
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("AddMasterItem")]
        [HttpPost]
        public async Task<object> AddMasterItem(MasterItem masterItem)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    string newCode = "";
                    //lets build the sequence here
                    var sequenceResult = db.MasterItems.Where(x => x.MasterItemTypeId == masterItem.MasterItemTypeId).OrderByDescending(x => x.Id).FirstOrDefault();
                    if (sequenceResult != null)
                    {
                        //lets generate a sequence now
                        //we need to get the number factor, which wil be everything but the first letter
                        int currentCounter = Convert.ToInt32(sequenceResult.Code.Substring(2).ToString());
                        currentCounter += 1;
                        newCode = sequenceResult.Code.Substring(0, 2).ToUpper().ToString();
                        //figure out how many 0's to add.
                        int currentCounterCount = currentCounter.ToString().Length;
                        int totalNumbers = 5;
                        int amountOfZerosNeeded = totalNumbers - currentCounterCount;
                        while (amountOfZerosNeeded > 0)
                        {
                            newCode = newCode + "0";
                            amountOfZerosNeeded -= 1;
                        }
                        newCode = newCode + currentCounter.ToString();
                    }
                    else
                    {
                        var code = db.MasterItemTypes.Where(x => x.MasterItemTypeId == masterItem.MasterItemTypeId).SingleOrDefault();
                        newCode = code.MasterItemTypeName.Substring(0, 2).ToUpper().ToString();
                        newCode = newCode + "00001";
                    }
                    masterItem.Code = newCode;
                    masterItem.CreatedBy = email;
                    db.MasterItems.Add(masterItem);
                    db.SaveChanges();
                    var result = await db.MasterItems.Include(x => x.MasterItemType).Include(y => y.MasterItemGroupMembers).ThenInclude(z => z.MasterItemGroup).OrderBy(x => x.MasterItemName).ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("UpdateMasterItem")]
        [HttpPost]
        public async Task<object> UpdateMasterItem(MasterItem masterItem)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {

                    //lets build the sequence here
                    var masterItemToUpdate = db.MasterItems.Where(x => x.Id == masterItem.Id).Include(y => y.MasterItemGroupMembers).ThenInclude(z => z.MasterItemGroup).FirstOrDefault();

                    masterItemToUpdate.MasterItemName = masterItem.MasterItemName;
                    masterItemToUpdate.Description = masterItem.Description;
                    //we need to check if a group is selected, and if so, add it, or update it.
                    // masterItemToUpdate.MasterItemGroupMembers.Clear();
                    if (masterItem.MasterItemGroupMembers != null && masterItem.MasterItemGroupMembers.Count > 0)
                    {
                        masterItemToUpdate.MasterItemGroupMembers = masterItem.MasterItemGroupMembers;
                    }
                    else
                    {
                        masterItemToUpdate.MasterItemGroupMembers = null;
                    }



                    if (masterItem.Dormant == true)
                    {
                        //it has now become dormant
                        masterItemToUpdate.Dormant = true;
                        masterItemToUpdate.DeletionDate = System.DateTime.Now;
                        masterItemToUpdate.DeletedBy = email;
                    }
                    db.MasterItems.Update(masterItemToUpdate);
                    db.SaveChanges();
                    var result = await db.MasterItems.Include(x => x.MasterItemType).Include(y => y.MasterItemGroupMembers).ThenInclude(z => z.MasterItemGroup).OrderBy(x => x.MasterItemName).ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("AddMasterItemInventoryItem")]
        [HttpPost]
        public async Task<object> AddMasterItemInventoryItem(MasterItemInventoryItem inventoryItem)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {

                    db.MasterItemInventoryItems.Add(inventoryItem);
                    db.SaveChanges();
                    var result = await db.MasterItemInventoryItems.Include(x => x.MasterItemId).OrderBy(x => x.MasterItem.Code).ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        //we need an api that would return locations, but we must limit the location based on certain criteria.
        //locations can be the team (IE Van), it can be the warehouses, or it can be at a store.
        //we need to create warehouses as well

        //lets manage warehouses first

        [Route("GetWarehouses")]
        [HttpGet]
        public async Task<object> GetWarehouses()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.Warehouse.Include(x => x.LocationType).Include(x => x.WarehouseManagers).OrderBy(x => x.WarehouseName).Include(x => x.Region).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Route("AddWarehouse")]
        [HttpPost]
        public async Task<object> AddWarehouse(Warehouse warehouse)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    warehouse.CreatedBy = email;
                    db.Warehouse.Add(warehouse);
                    db.SaveChanges();
                    var results = await db.Warehouse.OrderBy(x => x.WarehouseName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("UpdateWarehouse")]
        [HttpPost]
        public async Task<object> UpdateWarehouse(Warehouse warehouse)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {

                    //lets build the sequence here
                    var warehouseToUpdate = db.Warehouse.Where(x => x.WarehouseId == warehouse.WarehouseId).FirstOrDefault();

                    warehouseToUpdate.WarehouseName = warehouse.WarehouseName;
                    warehouseToUpdate.WarehouseCode = warehouse.WarehouseCode;
                    warehouseToUpdate.LocationTypeId = warehouse.LocationTypeId;
                    warehouseToUpdate.RegionId = warehouse.RegionId;
                    warehouseToUpdate.ApplicationUserId = warehouse.ApplicationUserId;
                    db.Warehouse.Update(warehouseToUpdate);
                    db.SaveChanges();
                    var results = await db.Warehouse.OrderBy(x => x.WarehouseName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetStores")]
        [HttpGet]
        public async Task<object> GetStores()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.Store
                        .Include(x => x.Region)
                        .ThenInclude(y => y.Chain)
                        .Include(z => z.InstallationDaysInstallationDay)
                        .Include(x => x.InstallationTeam)
                        .Where(x => x.Region.Chain.Dormant == false)
                        .Select(x => new
                        {
                            x.StoreId,
                            x.StoreName,
                            x.StoreNumber,
                            //x.Dormant,
                            x.DateCreated,
                            x.CreatedBy,
                            x.Region.Chain.ChainName,
                            x.InstallationDaysInstallationDay,
                            x.InstallationTeam,
                            installationTeamDisplayName = x.InstallationTeam.InstallationTeamName,
                            installationDayName = x.InstallationDaysInstallationDay.InstallationDay1
                        })
                        .OrderBy(x => x.StoreName).ToListAsync();
                    //Undo this start
                    var json = JsonSerializer.Serialize(results);
                    //Undo this end

                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("GetStoresByFilter")]
        [HttpPost]
        public async Task<object> GetStoresByFilter(string teamName)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.Store
                        .Include(x => x.Region)
                        .ThenInclude(y => y.Chain)
                        .Include(z => z.InstallationDaysInstallationDay)
                        .Include(x => x.InstallationTeam)

                        .Where(x => x.Dormant == false && x.InstallationTeam.InstallationTeamName == teamName)
                        .Select(x => new { x.StoreId, x.StoreName, x.StoreNumber, x.DateCreated, x.CreatedBy, x.Region.Chain.ChainName, x.InstallationDaysInstallationDay, x.InstallationTeam, installationTeamDisplayName = x.InstallationTeam.InstallationTeamName, installationDayName = x.InstallationDaysInstallationDay.InstallationDay1 }).ToListAsync();




                    //foreach(var item in results)
                    //{
                    //    foreach (var col in item.GetType().GetProperties())
                    //    {
                    //        //find where they match.
                    //        foreach (FiltersForList filtersForList in filters.filtersForLists)
                    //        {
                    //            if (filtersForList.field == col.Name)
                    //            {

                    //                results.Select(i => {
                    //                    try
                    //                    {
                    //                        dynamic item = i;
                    //                        return item.Prop == filtersForList.value;
                    //                    }
                    //                    catch (RuntimeBinderException)   //this type doesn't contain the property
                    //                    {
                    //                        return false;
                    //                    }
                    //                });

                    //                //this needs to be filtered
                    //                // results = results.Where(x => x.GetType().GetProperties().Where( y => y.Name == col.Name).ToList());
                    //            }
                    //        }

                    //    }
                    //}


                    return results.OrderBy(x => x.StoreName).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetLocationsForItems")]
        [HttpGet]
        public async Task<object> GetLocationsForItems(string locationType)
        {
            try
            {
                if (locationType != "")
                {
                    //lets see what location type to return
                    if (locationType == "NWH" || locationType == "REGIONS")
                    {
                        using (var db = new NovaDBContext())
                        {
                            var results = await db.Warehouse.Include(x => x.LocationType).OrderBy(x => x.WarehouseName).ToListAsync();
                            return results;
                        }
                    }

                    if (locationType == "TEAMS")
                    {
                        using (var db = new NovaDBContext())
                        {
                            var results = await db.InstallationTeam.OrderBy(x => x.InstallationTeamName).ToListAsync();
                            return results;
                        }
                    }
                }

                using (var db = new NovaDBContext())
                {
                    var results = await db.Store.OrderBy(x => x.StoreName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetInventoryItems")]
        [HttpGet]
        public async Task<object> GetInventoryItems()
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    //var results = await db.MasterItemInventoryItems
                    //    .Include(MasterItem => MasterItem.MasterItem).ThenInclude(y => y.MasterItemType)
                    //    .Include(z => z.MasterItem.itemsBeingMoved)
                    //    .Include(a => a.Warehouse).Include(c => c.Store)
                    //    .GroupBy(x => new { x.MasterItem.MasterItemName, x.Warehouse.WarehouseName, x.MasterItem.MasterItemType.MasterItemTypeName, x.MasterItem.itemsBeingMoved})
                    //    .Select(x => new { x.Key.MasterItemName, x.Key.WarehouseName, qtyOnHand = x.Count(), x.Key.MasterItemTypeName})
                    //    .OrderBy(x => x.MasterItemName).ToListAsync();
                    //return results;

                    var items = db.InvetoryItemsWithMovement
                   .FromSqlRaw("select mi.id, mi.MasterItemName,w.WarehouseName,mt.MasterItemTypeName, team.InstallationTeamName, s.storeName, count(mii.id) as qtyOnHand, count(iim.MasterItemInvenoryItemId) as qtyBeingMoved,"
                                + " count(mii.id) - count(iim.MasterItemInvenoryItemId) as qtyAtWarehouse, ROW_NUMBER() over (partition by mi.MasterItemName order by mi.masterItemName ) as ThisIsIt"
                                + ",case when team.InstallationTeamName is not null then team.InstallationTeamName when s.storeName is not null then s.storeName when w.WarehouseName is not null then w.WarehouseName else 'Not Set' end as SourceLocation "
                                + " from ops.MasterItemInventoryItem mii"
                                + " left outer join store.Warehouse w on w.WarehouseID = mii.WarehouseId"
                                 + " left outer join store.store s on s.StoreID = mii.StoreId"
                                 + " left outer join ops.InstallationTeam team on team.installationTeamId = mii.InstallationTeamId"
                                + " inner join ops.MasterItem mi on mi.id = mii.MasterItemId"
                                + " inner join ops.MasterItemType mt on mt.MasterItemTypeID = mi.MasterItemTypeId"
                                + " left outer join ops.InventoryItemMovement iim on iim.MasterItemInvenoryItemId = mii.id and iim.[FromWarehouseId] = w.WarehouseID and  iim.masterItemId = mi.id"
                                + " group by mi.MasterItemName, w.WarehouseName, mt.MasterItemTypeName,mi.id, mi.categoryid,mi.code,team.InstallationTeamName,s.storeName")
                   .ToList();

                    return items;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("AddInventoryItems")]
        [HttpPost]

        public async Task<object> AddInventoryItems(List<MasterItemInventoryItem> MasterItemInventoryItems)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;
                    db.MasterItemInventoryItems.AddRange(MasterItemInventoryItems);
                    db.SaveChanges();
                    var items = db.InvetoryItemsWithMovement
                   .FromSqlRaw("select mi.id, mi.MasterItemName,w.WarehouseName,mt.MasterItemTypeName, team.InstallationTeamName, s.storeName, count(mii.id) as qtyOnHand, count(iim.MasterItemInvenoryItemId) as qtyBeingMoved,"
                                + " count(mii.id) - count(iim.MasterItemInvenoryItemId) as qtyAtWarehouse, ROW_NUMBER() over (partition by mi.MasterItemName order by mi.masterItemName ) as ThisIsIt"
                                + ",case when team.InstallationTeamName is not null then team.InstallationTeamName when s.storeName is not null then s.storeName when w.WarehouseName is not null then w.WarehouseName else 'Not Set' end as SourceLocation "
                                + " from ops.MasterItemInventoryItem mii"
                                + " left outer join store.Warehouse w on w.WarehouseID = mii.WarehouseId"
                                 + " left outer join store.store s on s.StoreID = mii.StoreId"
                                 + " left outer join ops.InstallationTeam team on team.installationTeamId = mii.InstallationTeamId"
                                + " inner join ops.MasterItem mi on mi.id = mii.MasterItemId"
                                + " inner join ops.MasterItemType mt on mt.MasterItemTypeID = mi.MasterItemTypeId"
                                + " left outer join ops.InventoryItemMovement iim on iim.MasterItemInvenoryItemId = mii.id and iim.[FromWarehouseId] = w.WarehouseID and  iim.masterItemId = mi.id"
                                + " group by mi.MasterItemName, w.WarehouseName, mt.MasterItemTypeName,mi.id, mi.categoryid,mi.code,team.InstallationTeamName,s.storeName")
                   .ToList();

                    return items;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetBarcodesForMasterItem")]
        [HttpPost]

        public async Task<object> GetBarcodesForMasterItem(MasterItemSearch masterItemSearch)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;
                    var results = await db.MasterItemInventoryItems
                        .Include(MasterItem => MasterItem.MasterItem).ThenInclude(y => y.MasterItemType)
                        .Include(a => a.Warehouse).Include(c => c.Store)
                        .Include(a => a.InstallationTeam)
                        .Where(x => x.MasterItem.MasterItemName == masterItemSearch.masterItemName && x.Warehouse.WarehouseName == masterItemSearch.warehouseName && x.MasterItem.MasterItemType.MasterItemTypeName == masterItemSearch.masterItemTypeName && x.InstallationTeam.InstallationTeamName == masterItemSearch.installationTeamName)
                        //.GroupBy(x => new { x.MasterItem.MasterItemName, x.Warehouse.WarehouseName, x.MasterItem.MasterItemType.MasterItemTypeName })
                        //.Select(x => new { x.Key.MasterItemName, x.Key.WarehouseName, qtyOnHand = x.Count(), x.Key.MasterItemTypeName })
                        .ToListAsync();

                    var itemsToExcude = db.InventoryItemMovements.ToList();

                    var finalItems = results.Where(c => !itemsToExcude.Any(x => x.MasterItemInvenoryItemId == c.Id)).ToList();

                    return finalItems.Select(x => new { x.Barcode, x.WarehouseId, x.Id, x.MasterItemId, x.MasterItem.MasterItemName }).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("GetBarcodesForMasterItemNew")]
        [HttpPost]
        public async Task<object> GetBarcodesForMasterItemNew(MasterItemSearch masterItemSearch)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var blogs = db.ItemMovementToAndFrom
                  .FromSqlRaw("select mii.id,mii.Barcode, mi.MasterItemName "
                       + " ,case when w.warehousename is not null then w.WarehouseName when s.StoreName is not null then s.StoreName when ins.InstallationTeamName is not null then ins.InstallationTeamName else 'none' end as CurrentLocation "
                       + "  ,case when iim.ToWarehouseId is not null then wt.WarehouseName when iim.ToVanId is not null then itt.InstallationTeamName else 'none' end as ToLocation "
                       + "  from ops.MasterItemInventoryItem mii"
                       + "  left outer join store.Warehouse w on w.WarehouseID = mii.WarehouseId"
                       + "  left outer join store.store s on s.StoreID = mii.StoreId"
                       + "  left outer join ops.InstallationTeam ins on ins.InstallationTeamID = mii.InstallationTeamId"
                       + "  inner join ops.MasterItem mi on mi.id = mii.MasterItemId"
                       + "  left outer join ops.InventoryItemMovement iim on iim.MasterItemInvenoryItemId = mii.id"
                       + "  left outer join store.Warehouse wt on wt.WarehouseID = iim.ToWarehouseId "
                       + "  left outer join ops.InstallationTeam itt on itt.InstallationTeamID = iim.ToVanId")
                  .Where(x => x.MasterItemName == masterItemSearch.masterItemName)
                  .ToList();
                    return blogs;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("MoveItemsToWarehouse")]
        [HttpPost]

        public async Task<object> MoveItemsToWarehouse(List<InventoryItemMovement> itemMovements)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;
                    db.InventoryItemMovements.AddRange(itemMovements);
                    db.SaveChanges();

                    //lets create the transactions
                    List<InventoryItemTransactions> lstTransactons = new List<InventoryItemTransactions>();
                    foreach (InventoryItemMovement item in itemMovements)
                    {
                        InventoryItemTransactions transactions = new InventoryItemTransactions();
                        transactions.Barcode = item.Barcode;
                        transactions.CreatedBy = item.CreatedBy;
                        transactions.CreationDate = System.DateTime.Now;
                        transactions.FromVanId = item.FromVanId;
                        transactions.FromWarehouseId = item.FromWarehouseId;
                        transactions.MasterItemId = item.MasterItemId;
                        transactions.MasterItemInvenoryItemId = item.MasterItemInvenoryItemId;
                        transactions.ToVanId = item.ToVanId;
                        transactions.ToWarehouseId = item.ToWarehouseId;

                        lstTransactons.Add(transactions);
                    }

                    db.InventoryItemTransactions.AddRange(lstTransactons);
                    db.SaveChanges();

                    var results = await db.InventoryItemMovements
                        .ToListAsync();


                    return results.Select(x => x.Barcode).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("MoveItemsFromVan")]
        [HttpPost]

        public async Task<object> MoveItemsFromVan(List<InventoryItemMovement> itemMovements)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;
                    //db.InventoryItemMovements.AddRange(itemMovements);
                    //db.SaveChanges();

                    //lets create the transactions
                    List<InventoryItemTransactions> lstTransactons = new List<InventoryItemTransactions>();
                    foreach (InventoryItemMovement item in itemMovements)
                    {
                        InventoryItemTransactions transactions = new InventoryItemTransactions();
                        transactions.Barcode = item.Barcode;
                        transactions.CreatedBy = item.CreatedBy;
                        transactions.CreationDate = System.DateTime.Now;
                        transactions.FromVanId = item.FromVanId;
                        transactions.FromWarehouseId = item.FromWarehouseId;
                        transactions.MasterItemId = item.MasterItemId;
                        transactions.MasterItemInvenoryItemId = item.MasterItemInvenoryItemId;
                        transactions.ToVanId = item.ToVanId;
                        transactions.ToWarehouseId = item.ToWarehouseId;

                        lstTransactons.Add(transactions);

                        //we also need to fix the other ones.

                        //get and update the masteriteminventoryitem
                        var updateValue = db.MasterItemInventoryItems.Where(x => x.Id == item.MasterItemInvenoryItemId).FirstOrDefault();
                        if (updateValue != null)
                        {
                            updateValue.WarehouseId = item.ToWarehouseId;
                            updateValue.StoreId = null;
                            updateValue.InstallationTeamId = null;
                            db.MasterItemInventoryItems.Update(updateValue);
                        }
                    }
                    db.InventoryItemTransactions.AddRange(lstTransactons);

                    db.SaveChanges();

                    var results = await db.InventoryItemMovements
                        .ToListAsync();


                    return results.Select(x => x.Barcode).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetMasterItemMovement/{masterItemId}")]
        [HttpGet]
        public async Task<object> GetMasterItemMovement(int masterItemId)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryItemMovements
                    .Include(a => a.FromWarehouse)
                    .Include(b => b.ToWarehouse)
                    .Include(c => c.FromInstallationTeam)
                    .Include(d => d.ToInstallationTeam)
                    //.Include(c => c.MasterItem)
                    .Where(x => x.MasterItemId == masterItemId)
                        .ToListAsync();


                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }


        [Route("GetPendingMasteritemMovementByWarehouse/{userId}")]
        [HttpGet]
        public async Task<object> GetPendingMasteritemMovementByWarehouse(string userId)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryItemMovements
                    .Include(a => a.FromWarehouse)
                    .Include(b => b.ToWarehouse)
                    .Include(c => c.MasterItem)
                    .Include(d => d.ToInstallationTeam)
                    .Include(e => e.FromInstallationTeam)

                    .Where(x => x.ToWarehouse.ApplicationUserId == userId || x.ToInstallationTeam.InstallationTeamId.ToString() == userId)
                        .ToListAsync();


                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }


        [Route("GetPendingMasteritemMovementByWarehouseGrouped/{userId}")]
        [HttpGet]
        public async Task<object> GetPendingMasteritemMovementByWarehouseGrouped(string userId)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryItemMovements
                    .Include(a => a.FromWarehouse)
                    .Include(b => b.ToWarehouse)
                    .Include(c => c.MasterItem)
                    .Where(x => x.ToWarehouse.ApplicationUserId == userId)
                        .ToListAsync();


                    return results.GroupBy(x => new { x.MasterItem.MasterItemName, x.MasterItem.Id, FromWarehouseName = x.FromWarehouse.WarehouseName, FromWarehouseId = x.FromWarehouse.WarehouseId, toWarehouseId = x.ToWarehouse.WarehouseId })
                        .Select(x => new { x.Key.Id, x.Key.MasterItemName, x.Key.FromWarehouseId, x.Key.FromWarehouseName, x.Key.toWarehouseId, qtyToRecieve = x.Count() }).ToList()
                        .OrderBy(x => x.Id).ToList();


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Route("GetBarcodeByValue")]
        [HttpGet]
        public async Task<object> GetBarcodeByValue(string barcode)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = db.MasterItemInventoryItems.Where(x => x.Barcode == barcode).Include(x => x.MasterItem).FirstOrDefault();
                    // .Where(x => x.Barcode == barcode).FirstOrDefault();
                    //we need to check that it is not currently being moved.
                    //check that result isn't currently being moved
                    if (results != null)
                    {
                        var isMoved = db.InventoryItemMovements.Where(x => x.MasterItemInvenoryItemId == results.Id).FirstOrDefault();
                        if (isMoved != null)
                        {
                            return results;
                            return Newtonsoft.Json.JsonConvert.SerializeObject("The Item is currently already being moved");
                        }
                        else
                        {
                            return results;
                        }

                    }

                    return results;


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }


        [Route("GetBarcodesForReceiving")]
        [HttpPost]
        public async Task<object> GetBarcodesForReceiving(BarcodesForReceivingSearch search)
        {
            //Querying with LINQ to Entities 

            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.InventoryItemMovements
                    .Include(a => a.FromWarehouse)
                    .Include(b => b.ToWarehouse).ThenInclude(y => y.WarehouseManagers)
                    .Include(c => c.MasterItem)
                    .Where(x => x.ToWarehouse.WarehouseManagers.Select(x => x.ApplicationUserId).Contains(email) && x.FromWarehouse.WarehouseId == search.fromWarehouseId && x.MasterItem.Id == search.Id && x.ToWarehouse.WarehouseId == search.toWarehouseId)
                        .ToListAsync();


                    return results;


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        [Route("ReveiveItemsAtWarehouse")]
        [HttpPost]

        public async Task<object> ReveiveItemsAtWarehouse(List<InventoryItemMovement> itemMovements)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    //what we need to do is remove them from the movement, but update their location.

                    //warehouse.CreatedBy = email;
                    //db.InventoryItemMovements.RemoveRange(itemMovements);
                    //db.SaveChanges();

                    //lets update
                    foreach (var itemToUpdate in itemMovements)
                    {

                        var itemToRemove = db.InventoryItemMovements.Where(x => x.Id == itemToUpdate.Id).FirstOrDefault();
                        db.InventoryItemMovements.Remove(itemToRemove);

                        var thisItem = db.MasterItemInventoryItems.Where(x => x.MasterItemId == itemToUpdate.MasterItemId && x.Id == itemToUpdate.MasterItemInvenoryItemId).FirstOrDefault();
                        thisItem.WarehouseId = itemToUpdate.ToWarehouseId;
                        db.MasterItemInventoryItems.Update(thisItem);
                        db.SaveChanges();
                    }

                    var results = await db.InventoryItemMovements
                        .ToListAsync();


                    return results.Select(x => x.Barcode).ToList();
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("GetShelves")]
        [HttpGet]
        public async Task<object> GetShelves()
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.Shelves.Include(x => x.Warehouse).OrderBy(x => x.ShelfName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("GetShelves/{warehouseId}")]
        [HttpGet]
        public async Task<object> GetShelvesById(int warehouseId)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.Shelves.Include(x => x.Warehouse).Where(x => x.Warehouse.WarehouseId == warehouseId).OrderBy(x => x.ShelfName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("AddShelves")]
        [HttpPost]
        public async Task<object> AddShelves(Shelf shelf)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    shelf.CreatedBy = email;
                    db.Shelves.Add(shelf);
                    db.SaveChanges();
                    var results = await db.Shelves.Include(x => x.Warehouse).OrderBy(x => x.ShelfName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("UpdateShelf")]
        [HttpPost]
        public async Task<object> UpdateShelf(Shelf shelf)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {

                    //lets build the sequence here
                    var shelfToUpdate = db.Shelves.Where(x => x.ShelfId == shelf.ShelfId).FirstOrDefault();

                    shelfToUpdate.ShelfName = shelf.ShelfName;
                    shelfToUpdate.ShelfCode = shelf.ShelfCode;
                    shelfToUpdate.ShelfRow = shelf.ShelfRow;
                    shelfToUpdate.ShelfColumn = shelf.ShelfColumn;
                    shelfToUpdate.WarehouseId = shelf.WarehouseId;
                    db.Shelves.Update(shelfToUpdate);
                    db.SaveChanges();
                    var results = await db.Shelves.Include(x => x.Warehouse).OrderBy(x => x.ShelfName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("GetMedia")]
        [HttpGet]
        public async Task<object> GetMedia()
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;

                    var results = await db.Media

                       .OrderBy(x => x.MediaName).ToListAsync();


                    return results;


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }


        [Route("GetCapexOpexByMedia/{mediaId}")]
        [HttpGet]
        public async Task<object> GetCapexOpexByMedia(int mediaId)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    var results = await db.MediaCapexOpex.Include(x => x.Media).Include(x => x.MasterItem).Where(x => x.MediaId == mediaId).Include(y => y.MasterItemGroup).OrderBy(x => x.MasterItem.MasterItemName).ToListAsync();
                    return results;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("AddUpdateOpexCapexMediaAmount")]
        [HttpPost]

        public async Task<object> AddUpdateOpexCapexMediaAmount(List<MediaCapexOpex> mediaCapexOpices)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;
                    foreach (MediaCapexOpex mediaCapexOpex in mediaCapexOpices)
                    {
                        if (mediaCapexOpex.MasterItemId != null)
                        {
                            var result = db.MediaCapexOpex.Where(x => x.MasterItemId == mediaCapexOpex.MasterItemId && x.MediaId == mediaCapexOpex.MediaId).FirstOrDefault();
                            if (result != null)
                            {
                                result.AmountRequired = mediaCapexOpex.AmountRequired;
                                db.MediaCapexOpex.Update(result);
                                db.SaveChanges();
                            }
                            else
                            {
                                db.MediaCapexOpex.Add(mediaCapexOpex);
                                db.SaveChanges();
                            }
                        }
                        else
                        {
                            var result = db.MediaCapexOpex.Where(x => x.MasterItemGroupId == mediaCapexOpex.MasterItemGroupId && x.MediaId == mediaCapexOpex.MediaId).FirstOrDefault();
                            if (result != null)
                            {
                                result.AmountRequired = mediaCapexOpex.AmountRequired;
                                db.MediaCapexOpex.Update(result);
                                db.SaveChanges();
                            }
                            else
                            {
                                db.MediaCapexOpex.Add(mediaCapexOpex);
                                db.SaveChanges();
                            }
                        }

                    }
                    return mediaCapexOpices;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("AddMasterItemGroup")]
        [HttpPost]
        public async Task<object> AddMasterItemGroup(MasterItemGroups MasterItemGroups)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

                using (var db = new NovaDBContext())
                {
                    MasterItemGroups.CreatedBy = email;
                    db.MasterItemGroups.Add(MasterItemGroups);
                    db.SaveChanges();
                    var result = await db.MasterItemGroups.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }



        [Route("UpdateMasterItemGroups")]
        [HttpPost]
        public async Task<object> UpdateMasterItemGroups(MasterItemGroups MasterItemGroups)
        {
            try
            {

                using (var db = new NovaDBContext())
                {
                    var result = db.MasterItemGroups.SingleOrDefault(s => s.MasterItemGroupId == MasterItemGroups.MasterItemGroupId);
                    result.MasterItemGroupName = MasterItemGroups.MasterItemGroupName;
                    result.Dormant = MasterItemGroups.Dormant;
                    db.SaveChanges();
                    var returnResult = await db.MasterItemGroups.ToListAsync();
                    return returnResult;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }

        }
        [Route("GetMasterItemGroups")]
        [HttpGet]
        public async Task<object> GetMasterItemGroups()
        {
            try
            {
                using (var db = new NovaDBContext())
                {

                    var result = await db.MasterItemGroups.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Route("StockSummary")]
        [HttpGet]
        public async Task<object> StockSummary()
        {
            try
            {
                using (var db = new NovaDBContext())
                {

                    var result = await db.MasterItemInventoryItems.Include(x => x.MasterItem).Select(x => new { x.Barcode, x.categoryName, x.MasterItem.MasterItemName }).ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Route("GetStockTake")]
        [HttpGet]
        public async Task<object> GetStockTake()
        {
            try
            {
                using (var db = new NovaDBContext())
                {

                    var result = await db.InventoryStockTake.ToListAsync();
                    return result;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Route("CreateStockTake")]
        [HttpPost]
        public async Task<object> CreateStockTake(InventoryStockTake inventoryStockTake)
        {
            var user = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

            try
            {
                return await _stockTakeService.CreateStockTakeAsync(inventoryStockTake, user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }


        [Route("GetStockTakeReport")]
        [HttpGet]
        public async Task<object> GetStockTakeReport(int id)
        {
            try
            {
                using (var db = new NovaDBContext())
                {

                    //lets get all the counter types.

                    var allCounter = db.InventoryStockTakeRole.ToList();
                    //now loop through all of them and create them as columns i think.
                    DataTable outputTable = new DataTable();
                    outputTable.Columns.Add("Location");
                    foreach (InventoryStockTakeRole role in allCounter)
                    {
                        outputTable.Columns.Add(role.RoleName);
                    }
                    outputTable.Columns.Add("IsMatching");
                    outputTable.Columns.Add("[Allow Finish]");
                    outputTable.Columns.Add("[Singed Off]");



                    //actually what we need now is to get the correct stuff
                    List<string> allowedUsers = new List<string>()
                    {
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>"
                    };
                    var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                    List<string> locations = new List<string>();
                    List<string> locationsVans = new List<string>();
                    InventoryStockTakeDetails user = new InventoryStockTakeDetails();

                    if (allowedUsers.Contains(email))
                    {
                        locations = db.InventoryStockTakeDetails.Where(x => x.InventoryStockTakeId == id).Select(x => x.Warehouse.WarehouseName).Distinct().ToList();
                        locationsVans = db.InventoryStockTakeDetails.Where(x => x.InventoryStockTakeId == id).Select(x => x.InstallationTeam.InstallationTeamName).Distinct().ToList();
                        user = db.InventoryStockTakeDetails.Where(x => x.InventoryStockTakeId == id).FirstOrDefault();
                    }
                    else
                    {
                        locations = db.InventoryStockTakeDetails.Where(x => x.InventoryStockTakeId == id && x.userName == email).Select(x => x.Warehouse.WarehouseName).Distinct().ToList();
                        locationsVans = db.InventoryStockTakeDetails.Where(x => x.InventoryStockTakeId == id && x.userName == email).Select(x => x.InstallationTeam.InstallationTeamName).Distinct().ToList();
                        user = db.InventoryStockTakeDetails.Where(x => x.userName == email && x.InventoryStockTakeId == id).FirstOrDefault();
                    }

                    List<string> allLocations = new List<string>();

                    foreach (string warehouse in locations)
                    {
                        if (warehouse != null && user != null)
                        {
                            allLocations.Add("WAREHOUSE: " + warehouse);
                        }

                    }
                    foreach (string installationTeam in locationsVans)
                    {
                        if (installationTeam != null && user != null)
                        {
                            allLocations.Add("VAN : " + installationTeam);
                        }

                    }
                    //now we have all locations


                    foreach (string location in allLocations)
                    {
                        DataRow newRow = outputTable.NewRow();
                        //now that we have the location, we need the counts and also the people doing the counts.
                        //get all the different counters for this locations.
                        List<InventoryStockTakeDetailsBarcodes> inventoryStockTakeDetailsBarcodes = new List<InventoryStockTakeDetailsBarcodes>();
                        if (location.Contains("VAN"))
                        {
                            inventoryStockTakeDetailsBarcodes = db.InventoryStockTakeDetailsBarcodes.Include(x => x.InventoryStockTakeDetails).ThenInclude(x => x.InventoryStockTakeRole).Where(x => x.InstallationTeam.InstallationTeamName == location.Replace("VAN : ", "") && x.InventoryStockTakeDetails.InventoryStockTakeId == id).ToList();
                        }
                        else if (location.Contains("WAREHOUSE"))
                        {
                            inventoryStockTakeDetailsBarcodes = db.InventoryStockTakeDetailsBarcodes.Include(x => x.InventoryStockTakeDetails).ThenInclude(x => x.InventoryStockTakeRole).Where(x => x.Warehouse.WarehouseName == location.Replace("WAREHOUSE: ", "") && x.InventoryStockTakeDetails.InventoryStockTakeId == id).ToList();
                        }

                        List<string> scanedBarcodesForChecking = new List<string>();

                        int totalCountForMatching = 0;
                        bool isMatchingAmounts = false;
                        bool isSignedOff = true;
                        newRow = outputTable.NewRow();
                        newRow["Location"] = location;

                        List<string> barcodesCounter1 = new List<string>();
                        List<string> barcodesCounter2 = new List<string>();
                        List<string> barcodesAnomalyCounter = new List<string>();
                        List<string> barcodesFinalChecker = new List<string>();

                        var masterItemIDs = inventoryStockTakeDetailsBarcodes.Select(x => x.MasterItemId).Distinct().ToList();

                        //Final Checker
                        bool isTotalMatching = true;

                        foreach (int masterItem in masterItemIDs)
                        {
                            foreach (InventoryStockTakeRole role in allCounter)
                            {
                                var details = inventoryStockTakeDetailsBarcodes.Where(x => x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName == role.RoleName && x.MasterItemId == masterItem && x.InventoryStockTakeDetails.InventoryStockTakeId == id).Distinct().ToList();
                                if (role.RoleName == "Counter 1")
                                {
                                    barcodesCounter1 = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                                }
                                if (role.RoleName == "Counter 2")
                                {
                                    barcodesCounter2 = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                                }
                                if (role.RoleName == "Anomily Counter")
                                {
                                    barcodesAnomalyCounter = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                                }
                                if (role.RoleName == "Final Checker")
                                {
                                    barcodesFinalChecker = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                                }
                                //we kinda need to build a list of the barcodes, but lets check counter 1 and counter 2 only, then more
                                //find if any 2 are the same.
                                //get the names for the role owner
                                List<string> users = new List<string>();
                                users = inventoryStockTakeDetailsBarcodes.Where(x => x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName == role.RoleName && x.InventoryStockTakeDetails.InventoryStockTakeId == id).Select(x => x.userName).Distinct().ToList();


                                //what we need to do is check that counter 1 and counter 2 match, if not check if any other match.
                                List<string> allScannedBarcodes = inventoryStockTakeDetailsBarcodes.Where(x => x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName == role.RoleName && x.InventoryStockTakeDetails.InventoryStockTakeId == id).Select(x => x.barcode).Distinct().ToList();
                                if (inventoryStockTakeDetailsBarcodes.Count == 0)
                                {
                                    isSignedOff = false;
                                }
                                isSignedOff = false;
                                //foreach (var item in inventoryStockTakeDetailsBarcodes)
                                //{
                                //    if (item.InventoryStockTakeDetails.isSignedOff == false)
                                //    {
                                //        isSignedOff = false;
                                //    }
                                //}


                                int totalCount = allScannedBarcodes.Count;

                                if (totalCountForMatching == 0)
                                {
                                    totalCountForMatching = totalCount;
                                }
                                if (totalCount != 0)
                                {
                                    if (totalCount == totalCountForMatching)
                                    {
                                        isMatchingAmounts = true;
                                    }
                                    else
                                    {
                                        isTotalMatching = false;
                                    }
                                }
                                var csvString = String.Join(",", totalCount);
                                if (users.Count > 0)
                                {
                                    csvString = csvString + " : " + users[0];
                                }

                                newRow[role.RoleName] = csvString.Replace("@primeinstore.co.za", "");

                            }

                            if (barcodesCounter1.Count > 0)
                            {
                                isMatchingAmounts = barcodesCounter1.OrderBy(i => i).SequenceEqual(
                                barcodesCounter2.OrderBy(i => i));
                            }
                            if (!isMatchingAmounts && barcodesAnomalyCounter.Count > 0)
                            {
                                isMatchingAmounts = barcodesCounter1.OrderBy(i => i).SequenceEqual(
                             barcodesAnomalyCounter.OrderBy(i => i));
                            }

                            if (!isMatchingAmounts && barcodesCounter2.Count > 0)
                            {
                                isMatchingAmounts = barcodesCounter2.OrderBy(i => i).SequenceEqual(
                             barcodesAnomalyCounter.OrderBy(i => i));
                            }

                            if (!isMatchingAmounts && barcodesFinalChecker.Count > 0)
                            {
                                isMatchingAmounts = barcodesCounter1.OrderBy(i => i).SequenceEqual(
                             barcodesFinalChecker.OrderBy(i => i));
                            }
                            if (!isMatchingAmounts && barcodesFinalChecker.Count > 0)
                            {
                                isMatchingAmounts = barcodesCounter2.OrderBy(i => i).SequenceEqual(
                             barcodesFinalChecker.OrderBy(i => i));
                            }
                            if (!isMatchingAmounts && barcodesFinalChecker.Count > 0)
                            {
                                isMatchingAmounts = barcodesAnomalyCounter.OrderBy(i => i).SequenceEqual(
                             barcodesFinalChecker.OrderBy(i => i));
                            }
                        }
                        //ok, lets do it by masterItem


                        var allStockTakeDetails = inventoryStockTakeDetailsBarcodes.Select(x => x.InventoryStockTakeDetails.isSignedOff).ToList();
                        if (allStockTakeDetails.Select(x => x).Contains(false))
                        {
                            isSignedOff = false;
                        }
                        else
                        {
                            isSignedOff = true;
                        }


                        newRow["IsMatching"] = isTotalMatching;
                        //newRow["[Allow Finish]"] = isMatchingAmounts;
                        newRow["[Singed Off]"] = isSignedOff;

                        //newRow["IsMatching"] = "NA";
                        newRow["[Allow Finish]"] = "NA";
                        //newRow["[Singed Off]"] = "NA";

                        outputTable.Rows.Add(newRow);
                    }
                    //var result = await db.InventoryStockTake.Include(x => x.InventoryStockTakeDetails)
                    //    .ThenInclude(x => x.InventoryStockTakeDetailsBarcodes)
                    //    .Where(x => x.InventoryStockTakeId == id).ToListAsync();
                    ////we actually need to change things here

                    //if(result != null)
                    //{

                    //}


                    DataView dv = outputTable.DefaultView;
                    dv.Sort = "Location asc";
                    DataTable sortedDT = dv.ToTable();

                    var myreulst = sortedDT;

                    return myreulst;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Route("GetStockTakeReportId")]
        [HttpPost]
        public async Task<object> GetStockTakeReportId(StockTakeDetailsModel model)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    if (model.location.Contains("WAREHOUSE"))
                    {
                        return db.InventoryStockTakeDetails.Include(x => x.Warehouse).Where(x => x.Warehouse.WarehouseName == model.location.Replace("WAREHOUSE: ", "")).FirstOrDefault();
                    }
                    else
                    {
                        return db.InventoryStockTakeDetails.Include(x => x.InstallationTeam).Where(x => x.InstallationTeam.InstallationTeamName == model.location.Replace("VAN : ", "")).FirstOrDefault();
                    }
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Route("GetStockTakeByLocation")]
        [HttpGet]
        public async Task<object> GetStockTakeByLocation(string location)
        {
            int id = 6;
            try
            {
                using (var db = new NovaDBContext())
                {
                    List<InventoryStockTakeDetailsBarcodes> inventoryStockTakeDetailsBarcodes = new List<InventoryStockTakeDetailsBarcodes>();
                    if (location.Contains("WAREHOUSE"))
                    {
                        inventoryStockTakeDetailsBarcodes = db.InventoryStockTakeDetailsBarcodes.Include(x => x.MasterItem).Where(x => x.Warehouse.WarehouseName == location.Replace("WAREHOUSE: ", "") && x.InventoryStockTakeDetails.InventoryStockTakeId == id).ToList();
                    }
                    else if (location.Contains("VAN"))
                    {
                        inventoryStockTakeDetailsBarcodes = db.InventoryStockTakeDetailsBarcodes.Include(x => x.MasterItem).Where(x => x.InstallationTeam.InstallationTeamName == location.Replace("VAN: ", "") && x.InventoryStockTakeDetails.InventoryStockTakeId == id).ToList();
                    }


                    return inventoryStockTakeDetailsBarcodes.Select(x => new { x.barcode, x.MasterItem.MasterItemName, x.userName, x.CreationDate, x.isNonExisting, x.isWrongCapex }).ToList();
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }

        [Route("GenerateStockTakeReportByLocation")]
        [HttpGet]
        public async Task<object> GenerateStockTakeReportByLocation(string location, int stockTakeId)
        {
            try
            {
                using (var db = new NovaDBContext())
                {
                    List<InventoryStockTakeDetailsBarcodes> inventoryStockTakeDetailsBarcodes = new List<InventoryStockTakeDetailsBarcodes>();
                    if (location.Contains("WAREHOUSE"))
                    {
                        inventoryStockTakeDetailsBarcodes = db.InventoryStockTakeDetailsBarcodes.Include(x => x.MasterItem).Include(x => x.InventoryStockTakeDetails).ThenInclude(x => x.InventoryStockTakeRole).Where(x => x.Warehouse.WarehouseName == location.Replace("WAREHOUSE: ", "") && x.InventoryStockTakeDetails.InventoryStockTakeId == stockTakeId).ToList();
                        //we definately need more info. IE, counters, total per capex


                    }
                    else if (location.Contains("VAN"))
                    {
                        inventoryStockTakeDetailsBarcodes = db.InventoryStockTakeDetailsBarcodes.Include(x => x.MasterItem).Include(x => x.InventoryStockTakeDetails).ThenInclude(x => x.InventoryStockTakeRole).Where(x => x.InstallationTeam.InstallationTeamName == location.Replace("VAN : ", "") && x.InventoryStockTakeDetails.InventoryStockTakeId == stockTakeId).ToList();
                    }


                    var groupedReport = inventoryStockTakeDetailsBarcodes.Select(x => new { x.barcode, x.MasterItem.MasterItemName, x.userName, x.CreationDate, x.isNonExisting, x.isWrongCapex, x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName }).GroupBy(x => new { x.MasterItemName, x.userName, x.RoleName }).Select(x => new { x.Key.userName, x.Key.MasterItemName, x.Key.RoleName, counted = x.Count() }).ToList();

                    List<string> counters = new List<string>();
                    counters = groupedReport.Select(x => x.RoleName).Distinct().ToList();


                    DataTable outputTable = new DataTable();
                    outputTable.Columns.Add("Stock");

                    foreach (string rolename in counters.OrderBy(x => x))
                    {
                        outputTable.Columns.Add(rolename);
                    }

                    outputTable.Columns.Add("isMatching");
                    DataRow newRow = outputTable.NewRow();

                    foreach (string masterItem in groupedReport.Select(x => x.MasterItemName).Distinct().ToList())
                    {

                        List<string> barcodesCounter1 = new List<string>();
                        List<string> barcodesCounter2 = new List<string>();
                        List<string> barcodesAnomalyCounter = new List<string>();
                        List<string> barcodesFinalChecker = new List<string>();
                        bool isMatchingAmounts = false;
                        //we need a matching counter here
                        //we need to get the count for each master item
                        newRow = outputTable.NewRow();
                        newRow["Stock"] = masterItem;
                        foreach (string role in counters)
                        {

                            var details = inventoryStockTakeDetailsBarcodes.Where(x => x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName == role && x.MasterItem.MasterItemName == masterItem && x.InventoryStockTakeDetails.InventoryStockTakeId == stockTakeId).Distinct().ToList();
                            if (role == "Counter 1")
                            {
                                barcodesCounter1 = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                            }
                            if (role == "Counter 2")
                            {
                                barcodesCounter2 = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                            }
                            if (role == "Anomily Counter")
                            {
                                barcodesAnomalyCounter = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                            }
                            if (role == "Final Checker")
                            {
                                barcodesFinalChecker = details.Select(x => x.barcode).Distinct().OrderBy(x => x).ToList();
                            }


                            List<string> users = groupedReport.Where(x => x.MasterItemName == masterItem && x.RoleName == role).Select(x => x.userName).ToList();

                            var csvString = String.Join(",", users);
                            string user = groupedReport.Where(x => x.MasterItemName == masterItem && x.RoleName == role).Select(x => x.userName).FirstOrDefault();
                            try
                            {
                                string userAndCount = "";
                                //lets try do it better.
                                var listOfStuff = groupedReport.Where(x => x.MasterItemName == masterItem && x.RoleName == role).Select(x => new { x.counted, x.userName }).ToList();
                                foreach (var item in listOfStuff)
                                {
                                    userAndCount += item.counted + " : " + item.userName.Replace("@primeinstore.co.za", "") + ",";
                                }
                                userAndCount = userAndCount.Remove(userAndCount.Length - 1, 1);
                                //newRow[role] = groupedReport.Where(x => x.MasterItemName == masterItem && x.RoleName == role).Select(x => x.counted).FirstOrDefault() + " : " + csvString.Replace("@primeinstore.co.za", "");
                                newRow[role] = userAndCount;


                            }
                            catch
                            {
                                newRow[role] = 0;
                            }


                        }

                        if (barcodesCounter1.Count > 0)
                        {
                            isMatchingAmounts = barcodesCounter1.OrderBy(i => i).SequenceEqual(
                            barcodesCounter2.OrderBy(i => i));
                        }
                        if (!isMatchingAmounts && barcodesAnomalyCounter.Count > 0)
                        {
                            isMatchingAmounts = barcodesCounter1.OrderBy(i => i).SequenceEqual(
                         barcodesAnomalyCounter.OrderBy(i => i));
                        }

                        if (!isMatchingAmounts && barcodesCounter2.Count > 0)
                        {
                            isMatchingAmounts = barcodesCounter2.OrderBy(i => i).SequenceEqual(
                         barcodesAnomalyCounter.OrderBy(i => i));
                        }

                        if (!isMatchingAmounts && barcodesFinalChecker.Count > 0)
                        {
                            isMatchingAmounts = barcodesCounter1.OrderBy(i => i).SequenceEqual(
                         barcodesFinalChecker.OrderBy(i => i));
                        }
                        if (!isMatchingAmounts && barcodesFinalChecker.Count > 0)
                        {
                            isMatchingAmounts = barcodesCounter2.OrderBy(i => i).SequenceEqual(
                         barcodesFinalChecker.OrderBy(i => i));
                        }
                        if (!isMatchingAmounts && barcodesFinalChecker.Count > 0)
                        {
                            isMatchingAmounts = barcodesAnomalyCounter.OrderBy(i => i).SequenceEqual(
                         barcodesFinalChecker.OrderBy(i => i));
                        }
                        newRow["isMatching"] = isMatchingAmounts;
                        outputTable.Rows.Add(newRow);
                    }



                    return outputTable;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }

        }



        [Route("FinishStockTake")]
        [HttpPost]

        public async Task<object> FinishStockTake(FinishLocation location)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    List<InventoryStockTakeDetails> inventoryStockTakeDetails = new List<InventoryStockTakeDetails>();

                    if (location.location.Contains("WAREHOUSE"))
                    {
                        inventoryStockTakeDetails = db.InventoryStockTakeDetails.Where(x => x.Warehouse.WarehouseName == location.location.Replace("WAREHOUSE: ", "")).ToList();
                        //we definately need more info. IE, counters, total per capex


                    }
                    else if (location.location.Contains("VAN"))
                    {
                        inventoryStockTakeDetails = db.InventoryStockTakeDetails.Where(x => x.InstallationTeam.InstallationTeamName == location.location.Replace("VAN : ", "")).ToList();
                    }

                    foreach (InventoryStockTakeDetails inventoryStockTake in inventoryStockTakeDetails)
                    {
                        inventoryStockTake.isSignedOff = true;
                        inventoryStockTake.signedOffBy = email;
                        db.InventoryStockTakeDetails.Update(inventoryStockTake);
                        db.SaveChanges();

                    }
                    return inventoryStockTakeDetails;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }


        [Route("StockTakeDetailReportByArea")]
        [HttpPost]

        public async Task<object> StockTakeDetailReportByArea(StockTakeDetailsReportFilter stockTakeDetailsReportFilter)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
                using (var db = new NovaDBContext())
                {
                    DataTable outputTable = new DataTable();


                    //InventoryStockTakeDetails inventoryStockTakeDetails = db.InventoryStockTakeDetails.Include( x => x.InventoryStockTakeDetailsBarcodes).Where(x => x.InventoryStockTakeDetailId == stockTakeDetails.InventoryStockTakeDetailId).FirstOrDefault();
                    //we definately need more info. IE, counters, total per capex
                    List<InventoryStockTakeDetailsBarcodes> inventoryStockTakeDetailsBarcodes = new List<InventoryStockTakeDetailsBarcodes>();

                    var results = db.InventoryStockTakeDetailsBarcodes.Include(x => x.InstallationTeam).Include(x => x.Warehouse).Include(x => x.MasterItem).Include(x => x.InventoryStockTakeDetails).ThenInclude(x => x.InventoryStockTakeRole).Where(x => x.MasterItem.MasterItemName == stockTakeDetailsReportFilter.masterItemName && x.InventoryStockTakeDetails.InventoryStockTakeId == stockTakeDetailsReportFilter.stockTakeId);

                    if (stockTakeDetailsReportFilter.location.Contains("WAREHOUSE"))
                    {
                        return results.Where(x => x.Warehouse.WarehouseName == stockTakeDetailsReportFilter.location.Replace("WAREHOUSE: ", "")).Select(x => new { x.MasterItem.MasterItemName, x.barcode, Location = x.Warehouse.WarehouseName, x.userName, x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName, x.CreationDate }).ToList();
                        //we definately need more info. IE, counters, total per capex


                    }
                    else if (stockTakeDetailsReportFilter.location.Contains("VAN"))
                    {
                        return results.Where(x => x.InstallationTeam.InstallationTeamName == stockTakeDetailsReportFilter.location.Replace("VAN : ", "")).Select(x => new { x.MasterItem.MasterItemName, x.barcode, Location = x.InstallationTeam.InstallationTeamName, x.userName, x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName, x.CreationDate }).ToList();
                    }
                    else
                    {
                        return null;
                    }
                    //ok, here we will do some work to find all the different roles

                    //List<string> lstRoles = new List<string>();
                    //lstRoles = inventoryStockTakeDetailsBarcodes.Select(x => x.InventoryStockTakeDetails.InventoryStockTakeRole.RoleName).Distinct().ToList();


                    //outputTable.Columns.Add("Stock");
                    //foreach (string rolename in lstRoles.OrderBy(x => x))
                    //{
                    //    outputTable.Columns.Add(rolename);
                    //}
                    //DataRow newRow = outputTable.NewRow();

                    //foreach (string rolename in lstRoles.OrderBy(x => x))
                    //{
                    //    newRow["stock"] = stockTakeDetailsReportFilter.masterItemName;

                    //    outputTable.Rows.Add(newRow);
                    //    newRow = outputTable.NewRow();
                    //}
                    //// return outputTable;

                    //return inventoryStockTakeDetailsBarcodes;
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }

        }

        [Route("GetBarcodePreviousLocation")]
        [HttpGet]
        public async Task<object> GetBarcodePreviousLocation(string barcode, string currentLocation)
        {
            using (var context = new NovaDBContext())
            {
                var results =
                    await context.InventoryItemTransactions.Include(x => x.FromStore).Where(x => x.FromStore.StoreId == x.FromStoreId)
                    .Include(x => x.ToStore).ThenInclude(y => y.Region).ThenInclude(z => z.Chain)
                    .Where(x => x.ToStore.StoreId == x.ToStoreId)
                    .Include(x => x.FromInstallationTeam).Where(x => x.FromInstallationTeam.InstallationTeamId == x.FromVanId)
                    .Include(x => x.ToInstallationTeam).Where(x => x.ToInstallationTeam.InstallationTeamId == x.ToVanId)
                    .Include(x => x.FromWarehouse).Where(x => x.FromWarehouse.WarehouseId == x.FromWarehouseId)
                    .Include(x => x.ToWarehouse).Where(x => x.ToWarehouse.WarehouseId == x.ToWarehouseId)
                    .Include(x => x.MasterItemInvenoryItem).ThenInclude(x => x.MasterItem)
                    .Include(x => x.ToStore).ThenInclude(x => x.InstallationTeam)
                    .Where(x => x.Barcode == barcode &&
                    (x.ToStore.StoreName != currentLocation ||
                     x.ToWarehouse.WarehouseName != currentLocation ||
                     x.ToInstallationTeam.InstallationTeamName != currentLocation))
                    .Select(x => new
                    {
                        Barcode = x.Barcode,
                        FromStore = x.FromStore.StoreName,
                        FromWarehouse = x.FromWarehouse.WarehouseName,
                        FromTeam = x.FromInstallationTeam.InstallationTeamName
                    })
                    .Distinct()
                    .FirstOrDefaultAsync();

                return results;
            }
        }

        [Route("GetLatestBarcodesLoaded")]
        [HttpGet]
        public async Task<object> GetLatestBarcodesLoaded(string barcode)
        {
            //Querying with LINQ to Entities 

            try
            {

                using (var db = new NovaDBContext())
                {
                    //warehouse.CreatedBy = email;
                    List<MasterItemInventoryItem> lstResults = new List<MasterItemInventoryItem>();
                    if (barcode != null && barcode != "")
                    {
                        lstResults = db.MasterItemInventoryItems.Where(x => x.Barcode.Contains(barcode)).Include(x => x.MasterItem).Include(x => x.InstallationTeam).Include(x => x.Store).Include(x => x.Warehouse).ToList();
                    }
                    else
                    {
                        lstResults = db.MasterItemInventoryItems.Include(x => x.MasterItem).Include(x => x.InstallationTeam).Include(x => x.Store).Include(x => x.Warehouse).OrderByDescending(x => x.CreationDate).Take(50).ToList();
                    }

                    return lstResults;


                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);

            }
        }

        public class regionMediaResult
        {
            public string creationDate { get; set; }
            public string contractNumber { get; set; }
            public string campaign { get; set; }

            public string mediaType { get; set; }
            public int totalAdsInstallationQty { get; set; }
            public int storelistcount { get; set; }
            public string contractId { get; set; }
            public string region { get; set; }

        }
        private static List<T> ConvertDataTable<T>(DataTable dt)
        {
            List<T> data = new List<T>();
            foreach (DataRow row in dt.Rows)
            {
                T item = GetItem<T>(row);
                data.Add(item);
            }
            return data;
        }
        private static T GetItem<T>(DataRow dr)
        {
            Type temp = typeof(T);
            T obj = Activator.CreateInstance<T>();

            foreach (DataColumn column in dr.Table.Columns)
            {
                foreach (PropertyInfo pro in temp.GetProperties())
                {
                    if (pro.Name == column.ColumnName)
                        pro.SetValue(obj, dr[column.ColumnName], null);
                    else
                        continue;
                }
            }
            return obj;
        }

        public class GetImageByPath
        {
            public string ImageUrl { get; set; }
        }

        public class FinishLocation
        {
            public string location { get; set; }
        }
        public class MasterItemSearch
        {
            public string masterItemName { get; set; }
            public string warehouseName { get; set; }
            public string masterItemTypeName { get; set; }

            public string installationTeamName { get; set; }
        }


        public class BarcodesForReceivingSearch
        {
            public int Id { get; set; }
            public int fromWarehouseId { get; set; }
            public int toWarehouseId { get; set; }

            public int toInstallationTeamId { get; set; }
        }

        public class BarcodeSearch
        {
            public string barcode { get; set; }

        }

        public class OpexCapexMasterItems
        {
            public int masterItemId;
            public int mediaId;
            public int amount;
            public string userName;

        }


        public class StoreFilterValues
        {
            public string chainName { get; set; }
            public string teamName { get; set; }
        }
        public class FiltersToApply
        {
            public List<FiltersForList> filtersForLists = new List<FiltersForList>();
        }

        public class FiltersForList
        {
            public string field { get; set; }
            public string value { get; set; }
        }

        public class InventoryStockTakeDetailsMasterItem
        {
            public int id { get; set; }
            public int masterItemId { get; set; }
            public int stockTakeId { get; set; }
            public bool isWrongCapex { get; set; }
            public int inventoryStockTakeDetailsBarcodesId { get; set; }
        }
        public class StockTakeDetailsReport
        {
            public string locationName { get; set; }

            public string scannerType { get; set; }

            public int totalScanned { get; set; }
        }

        public class StockTakeDetailsReportFilter
        {
            public string location { get; set; }
            public string masterItemName { get; set; }
            public int stockTakeId { get; set; }
        }
    }
    public class StockTakeDetailsModel
    {
        public string location { get; set; }
        public int stockTakeId { get; set; }
    }

    public class IncomingMediaPerRegionRevisedResult
    {
        public IncomingMediaPerRegionRevised IncomingMediaPerRegionRevised { get; set; }
    }
}
