﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PhoenixAPI.Models.IncomingMedia
{
    public class IncomingMediaPerRegionRevised
    {
        public int Id { get; set; }
        public Guid ContractId { get; set; }
        public string? Campaign { get; set; }
        public string? ContractNumber { get; set; }
        public string? Chain { get; set; }
        public string? MediaType { get; set; }
        public int? RegionId { get; set; }
        public string? RegionName { get; set; }
        public string? DeliveryNote { get; set; }
        public int? ClosingBalanceQty { get; set; }
        public int? QuantityReceived { get; set; }
        public int? MediaDistributedQty { get; set; }
        public int? AvailableMediaQty { get; set; }
        public int? MediaRequiredQty { get; set; }
        public int? MediaRequestedQty { get; set; }
        public int? SpareQty { get; set; }
        public int? ShortQty { get; set; }
        public string? RegionReceivingMediaRequest { get; set; }
        public string? DistributionDate { get; set; }
        public string? Comment { get; set; }
        public string? DistributedBy { get; set; }
        public bool IsMediaReceivedByRequestedRegion { get; set; }
    }
}
