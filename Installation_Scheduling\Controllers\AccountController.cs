﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using PhoenixAPI.Entities;
using PhoenixAPI.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace PhoenixAPI.Controllers
{
    [EnableCors("EnableCORS")]
    [Route("[controller]/[action]")]
    public class AccountController : Controller
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IConfiguration _configuration;

        public AccountController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IConfiguration configuration
            )
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _configuration = configuration;
        }

        [HttpPost]
        public async Task<object> Login([FromBody] LoginDto model)
        {
            var result = await _signInManager.PasswordSignInAsync(model.Email, model.Password, false, false);
            
            if (result.Succeeded)
            {
                var appUser = _userManager.Users.SingleOrDefault(r => r.Email == model.Email && r.isEnabled == true);
                return await GenerateJwtToken(model.Email, appUser);
            }

            throw new ApplicationException("INVALID_LOGIN_ATTEMPT");
        }


       

        [HttpPost]
        public async Task<object> Register([FromBody] RegisterDto model)
        {
            var user = new ApplicationUser
            {
                FirstName = model.FirstName,
                LastName = model.Surname,
                UserName = model.Email,
                PhoneNumber = model.PhoneNumber,
                Email = model.Email
            };

            if(model.Password == null)
            {
                //lets generate a password here, and we also now need to start sending emails, and verification emails
                model.Password = "CreateSecretPassword@1";
            }

            //_userManager.ChangePasswordAsync();

            IdentityResult identityResult = new IdentityResult();


            var appUser = _userManager.Users.SingleOrDefault(r => r.Email == model.Email);

            identityResult = await _userManager.CreateAsync(user, model.Password);

            if (!identityResult.Succeeded)
            {
                throw new ApplicationException("UNKNOWN_ERROR");
            }

            return StatusCode(200);
        }

        [HttpPost]
        public async Task<object> UpdateUser([FromBody] RegisterDto model)
        {
            var appUser = _userManager.Users.SingleOrDefault(r => r.Email == model.Email);
            appUser.FirstName = model.FirstName;
            appUser.LastName = model.Surname;
            appUser.PhoneNumber = model.PhoneNumber;
            appUser.EmailConfirmed = model.EmailConfirmed;
            appUser.isEnabled = model.isEnabled;
            if((model.Password != null) && (model.Password.Length > 0))
            {
                
            }
            var result = await _userManager.UpdateAsync(appUser);

            //we need to change the roles as well.

           
                var userRoles = await _userManager.GetRolesAsync(appUser);

                //we need to do something else here.
                //find roles to remove
                var rolesToRemove = userRoles.Where(p => !model.selectedNewRoles.Any(p2 => p2 == p));
                //find roles to add
                var rolesToAdd = model.selectedNewRoles.Where(p => !userRoles.Any(p2 => p2 == p));
                try
                {
                        var resultRolesRemoved = await _userManager.RemoveFromRolesAsync(appUser, rolesToRemove);
                        var resultRolesAdded = await _userManager.AddToRolesAsync(appUser, rolesToAdd);
                }
                catch (Exception ex)
                {
                    string error = ex.Message;
                }
         

            if (result.Succeeded)
            {
                return appUser;
            }
            else
            {
                return result.Errors.FirstOrDefault().Description;
            }


        }

        private async Task<object> GenerateJwtToken(string email, ApplicationUser user)
        {
            var userRoles = await _userManager.GetRolesAsync(user);
            string userName = await _userManager.GetUserNameAsync(user);
            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.NameIdentifier, user.Id)
            };

            claims.AddRange(userRoles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));


            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JwtKey"]));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expires = DateTime.Now.AddDays(Convert.ToDouble(_configuration["JwtExpireDays"]));

            var token = new JwtSecurityToken(
                _configuration["JwtIssuer"],
                _configuration["JwtIssuer"],
                claims,
                expires: expires,
                signingCredentials: creds
            );
            var tokenString = new JwtSecurityTokenHandler().WriteToken(token);
            return Ok(new { Token = tokenString, Name = userName, UserRoles = userRoles });
            //return new JwtSecurityTokenHandler().WriteToken(token);
        }


        [HttpGet]
        [Route("{username?}")]
        public async Task<object> GetExistingUsers(string username = "")
        {
            if (username == "")
            {
                var users = await _userManager.Users.ToListAsync();
                List<ApplicationUser> lstUsers = new List<ApplicationUser>();
                List<UserLists> lstAllUsers = new List<UserLists>();
                foreach (ApplicationUser user in users)
                {
                    UserLists userLists = new UserLists();
                    userLists.user = user;
                    var userRoles = await _userManager.GetRolesAsync(user);
                    var claims = new List<Claim>();

                    claims.AddRange(userRoles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));

                    List<string> lstClaimValue = new List<string>();
                    foreach (Claim cl in claims)
                    {
                        lstClaimValue.Add(cl.Value);

                    }
                    userLists.userRoles = lstClaimValue;
                    lstAllUsers.Add(userLists);
                }
                return Ok(new { Users = lstAllUsers });
            }
            else
            {
                var user = _userManager.Users.SingleOrDefault(r => r.Email == username);
                List<ApplicationUser> lstUsers = new List<ApplicationUser>();
                UserLists userLists = new UserLists();

                var userRoles = await _userManager.GetRolesAsync(user);
                var claims = new List<Claim>();

                claims.AddRange(userRoles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));

                List<string> lstClaimValue = new List<string>();
                foreach (Claim cl in claims)
                {
                    lstClaimValue.Add(cl.Value);

                }
                userLists.user = user;
                userLists.userRoles = lstClaimValue;


                return Ok(new { User = userLists });
            }



        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<object> GetAllUsers()
        {
            var users = await _userManager.Users.ToListAsync();
            List<ApplicationUser> lstUsers = new List<ApplicationUser>();
            List<UserLists> lstAllUsers = new List<UserLists>();
            foreach (ApplicationUser user in users)
            {
                UserLists userLists = new UserLists();
                userLists.user = user;
                var userRoles = await _userManager.GetRolesAsync(user);
                var claims = new List<Claim>();

                claims.AddRange(userRoles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));

                List<string> lstClaimValue = new List<string>();
                foreach (Claim cl in claims)
                {
                    lstClaimValue.Add(cl.Value);

                }
                userLists.userRoles = lstClaimValue;
                lstAllUsers.Add(userLists);
            }
            return Ok(new { Users = lstAllUsers });

        }

        [HttpPost]
        public async Task<object> AddRemoveRole([FromBody]UserLists userLists)
        {

            var appUser = _userManager.Users.SingleOrDefault(r => r.Email == userLists.user.Email);
            var userRoles = await _userManager.GetRolesAsync(appUser);
            try

            {
                if (userRoles.Intersect(userLists.userRoles).Any())
                {
                    var result = await _userManager.RemoveFromRolesAsync(appUser, userLists.userRoles);
                }
                else
                {
                    var result = await _userManager.AddToRolesAsync(appUser, userLists.userRoles);
                }
            }
            catch (Exception ex)
            {
                string error = ex.Message;
            }

            return Ok(new { user = userLists });
        }

        [HttpGet]
        [Authorize(Roles = "Admin")]
        public  List<IdentityRole> GetAllRoles()
        {
            using (UserDbContext dbContext = new UserDbContext())
            {
                var roleStore = new RoleStore<IdentityRole>(dbContext);
                return roleStore.Roles.ToList();
            }
        }


      
        [HttpGet]
        public async Task<object> GetTeamManagers()
        {

            var users = await _userManager.Users.ToListAsync();
            List<ApplicationUser> lstUsers = new List<ApplicationUser>();
            List<UserLists> lstAllUsers = new List<UserLists>();
            foreach (ApplicationUser user in users)
            {
                UserLists userLists = new UserLists();
                userLists.user = user;
                var userRoles = await _userManager.GetRolesAsync(user);
                var claims = new List<Claim>();

                claims.AddRange(userRoles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));

                List<string> lstClaimValue = new List<string>();
                foreach (Claim cl in claims)
                {
                    lstClaimValue.Add(cl.Value);

                }
                userLists.userRoles = lstClaimValue;
                if(userLists.userRoles.Contains("TeamManager"))
                {
                    lstAllUsers.Add(userLists);
                }
                
            }
            return Ok(new { Users = lstAllUsers.GroupBy(x => x.user.UserName).Select(y => y.Key) });

        }

        [HttpGet]
        public async Task<object> GetTeamUsers()
        {

            var users = await _userManager.Users.ToListAsync();
            List<ApplicationUser> lstUsers = new List<ApplicationUser>();
            List<UserLists> lstAllUsers = new List<UserLists>();
            foreach (ApplicationUser user in users)
            {
                UserLists userLists = new UserLists();
                userLists.user = user;
                var userRoles = await _userManager.GetRolesAsync(user);
                var claims = new List<Claim>();

                claims.AddRange(userRoles.Select(role => new Claim(ClaimsIdentity.DefaultRoleClaimType, role)));

                List<string> lstClaimValue = new List<string>();
                foreach (Claim cl in claims)
                {
                    lstClaimValue.Add(cl.Value);

                }
                userLists.userRoles = lstClaimValue;
                if (userLists.userRoles.Contains("MyMobilityUser"))
                {
                    lstAllUsers.Add(userLists);
                }

            }
            return Ok(new { Users = lstAllUsers.GroupBy(x => x.user.UserName).Select(y => y.Key) });

        }


        public async Task<ActionResult> ForgotPassword([FromBody] RegisterDto model)
        {
            try
            {
                var user = await _userManager.FindByNameAsync(model.Email);
                var code = await _userManager.GeneratePasswordResetTokenAsync(user);
                //if (user == null || !(await _userManager.IsEmailConfirmedAsync(user)))
                //{
                //    // Don't reveal that the user does not exist or is not confirmed
                //    return StatusCode(500, "An error has occured");
                //}
                //we will need to send an email now.

                HelperController helperController = new HelperController();

                HelperController.EmailData emailData = new HelperController.EmailData();
                 emailData.recipient = user.Email;

                emailData.carbonCopies.Add("<EMAIL>");
                emailData.textToSend = code;

                HelperController.ReplacementValues replacementValues = new HelperController.ReplacementValues();
                replacementValues.StringToFind = "[[FullText]]";
                string encodedToken = WebUtility.UrlEncode(code);
                string url = "https://phoenix.primeinstore.co.za/resetPassword" + "?token=" +encodedToken;
                replacementValues.valueToReplaceWith = "Please click on the following link to reset your password : " + url;
                emailData.replacementValuesHtml.Add(replacementValues);
                helperController.sendEmail(emailData);
                return Ok(new { result = "Password succesfully reset" });
            }
            catch(Exception ex)
            {
                return StatusCode(500, new { result = "Something went wrong, please try again" });
            }
           

            // If we got this far, something failed, redisplay form
            return null;
        }


        public async Task<ActionResult> ResetPassword([FromBody] ResetPasswordModel model)
        {
            var user = await _userManager.FindByNameAsync(model.Email);
            try
            {
                
                var resetResult = await _userManager.ResetPasswordAsync(user, model.Code, model.NewPassword);
                //await _userManager.GeneratePasswordResetTokenAsync(user);
                if(resetResult.Succeeded)
                {
                    return Ok(new { result = "Password succesfully changed, please login again" });
                }
                else
                {
                    return StatusCode(500, new { result = resetResult.Errors });
                }
              
            }
            catch(Exception ex)
            {
                return StatusCode(500, new { result = "Something went wrong, please try again" });
            }
           
        }



        //DANGEROUS CODE, NEVER TO BE PUBLISHED.
        //[HttpPost]
        //public async Task<object> ImportUserList(string fileName)
        //{
        //    try
        //    {
        //        fileName = @"C:\Users\<USER>\Downloads\UsersToImport.xlsx";

        //        List<UsersImported> lstUsersImported = new List<UsersImported>();



        //        DataTable dt = new DataTable();
        //        using (XLWorkbook workbook = new XLWorkbook(fileName))
        //        {
        //            IXLWorksheet worksheet = workbook.Worksheet(1);
        //            bool FirstRow = true;
        //            //Range for reading the cells based on the last cell used.  
        //            string readRange = "1:1";
        //            foreach (IXLRow row in worksheet.RowsUsed())
        //            {
        //                //If Reading the First Row (used) then add them as column name  
        //                if (FirstRow)
        //                {
        //                    //Checking the Last cellused for column generation in datatable  
        //                    readRange = string.Format("{0}:{1}", 1, row.LastCellUsed().Address.ColumnNumber);
        //                    foreach (IXLCell cell in row.Cells(readRange))
        //                    {
        //                        dt.Columns.Add(cell.Value.ToString());
        //                    }
        //                    FirstRow = false;
        //                }
        //                else
        //                {
        //                    //Adding a Row in datatable  
        //                    dt.Rows.Add();
        //                    int cellIndex = 0;
        //                    //Updating the values of datatable  
        //                    foreach (IXLCell cell in row.Cells(readRange))
        //                    {
        //                        dt.Rows[dt.Rows.Count - 1][cellIndex] = cell.Value.ToString();
        //                        cellIndex++;
        //                    }
        //                }
        //            }
        //        }

        //        Parallel.ForEach(dt.Rows.Cast<DataRow>(), dr =>
        //        {
        //            //here we need to conver the rows


        //            UsersImported user = new UsersImported();

        //            try
        //            {
        //                //lets try to add it here

        //                //itemInventoryItem.Code = dr["Code"].ToString();
        //                //itemInventoryItem.qty = Convert.ToInt32(dr["Qty On Hand"].ToString());
        //                //itemInventoryItem.Location = dr["Current Location"].ToString();
        //                //itemInventoryItem.Item = dr["Description"].ToString();
        //                user.FirstName = dr["First Name"].ToString();
        //                user.Surname = dr["Surname"].ToString();
        //                user.TeamName = dr["Team Name"].ToString();
        //                user.CellNumber = dr["Personal Cell Nr"].ToString();



        //                lstUsersImported.Add(user);

        //            }
        //            catch (Exception ex)
        //            {
        //                // await ExceptionController.HandleException(ex);
        //            }


        //        });





        //        foreach (UsersImported user in lstUsersImported)
        //        {
        //            string abcd = user.FirstName;

        //            user.EmailAddress = user.FirstName.ToLower().Replace(" ", "") + user.Surname.Replace(" ", "").Substring(0, 1).ToLower() + "@primeinstore.co.za";
        //            user.Password = user.FirstName + "@" + user.CellNumber.Replace(" ", "").Replace("-", "").Substring(6, 4);
        //            user.Password = user.Password.Replace(" ", "");

        //            RegisterDto registerDto = new RegisterDto();
        //            registerDto.Email = user.EmailAddress;
        //            registerDto.EmailConfirmed = true;
        //            registerDto.FirstName = user.FirstName;
        //            registerDto.isEnabled = true;
        //            registerDto.Password = user.Password;
        //            registerDto.PhoneNumber = user.CellNumber;
        //            registerDto.selectedNewRoles = new List<string> { ("MyMobilityUser") };
        //            registerDto.Surname = user.Surname;
        //            try
        //            {
        //                // await Register(registerDto);
        //                if(user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                 }
        //                else if(user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                }
        //                else if (user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                }
        //                else if (user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                }
        //                else if (user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                }
        //                else if (user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                }
        //                else if (user.EmailAddress == "<EMAIL>")
        //                {
        //                    var appUser = _userManager.Users.SingleOrDefault(r => r.Email == user.EmailAddress);
        //                    string token = await _userManager.GeneratePasswordResetTokenAsync(appUser);

        //                    await _userManager.ResetPasswordAsync(appUser, token, user.Password);
        //                }
        //                else
        //                {

        //                }


        //            }
        //            catch
        //            {

        //            }

        //        }

        //        //List<StockSummary> lstNationalWarehouseItems = stockSummariesThousand.Where(x => x.WarehouseId == 236).ToList();

        //        //ok, once we have the data, we can look at importing it


        //        return null;
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, ex.InnerException.Message);

        //    }

        //}

        public class UsersImported
        {
            public string FirstName { get; set; }
            public string Surname { get; set; }
            public string TeamName { get; set; }
            public string CellNumber { get; set; }
            public string EmailAddress { get; set; }

            public string Password { get; set; }
        }

        //these need to move to a model
        public class LoginDto
        {
            [Required]
            public string Email { get; set; }

            [Required]
            public string Password { get; set; }

        }

        public class RegisterDto
        {
            [Required]
            public string Email { get; set; }

            [Required]
            public string FirstName { get; set; }

            [Required]
            public string Surname { get; set; }

            [Required]
            public string PhoneNumber { get; set; }

            [Required]
            [StringLength(100, ErrorMessage = "PASSWORD_MIN_LENGTH", MinimumLength = 6)]
            public string Password { get; set; }

            public bool EmailConfirmed { get; set; }
            public bool isEnabled { get; set; }

            public List<string> selectedNewRoles { get; set; }
        }


        public class ResetPasswordModel
        {
            [Required]
            public string Code { get; set; }

            [Required]
            public string NewPassword { get; set; }

            [Required]
            public string Email { get; set; }
        }
    }
}
