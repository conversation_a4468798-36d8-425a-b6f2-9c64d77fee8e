﻿using System;
using System.Collections.Generic;

#nullable disable

namespace PhoenixAPI.Models
{
    public partial class VwListofSystemuser
    {
        public Guid Userid { get; set; }
        public string Username { get; set; }
        public string Identity { get; set; }
        public string Firstname { get; set; }
        public string Lastname { get; set; }
        public string Email { get; set; }
        public bool Enabled { get; set; }
        public DateTime? Dateofpasswordcreation { get; set; }
        public DateTime? Dateofexpiration { get; set; }
        public bool Passwordexpired { get; set; }
        public DateTime? Dateoflastsuccessfullogin { get; set; }
        public string Fullname { get; set; }
    }
}
