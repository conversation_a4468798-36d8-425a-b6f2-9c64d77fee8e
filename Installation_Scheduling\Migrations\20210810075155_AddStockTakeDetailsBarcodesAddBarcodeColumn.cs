﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddStockTakeDetailsBarcodesAddBarcodeColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "barcode",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "barcode",
                schema: "Ops",
                table: "InventoryStockTakeDetailsBarcodes");
        }
    }
}
