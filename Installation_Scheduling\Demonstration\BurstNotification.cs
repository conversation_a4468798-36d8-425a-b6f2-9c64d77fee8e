﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PhoenixAPI.Demonstration
{
    public class BurstNotification
    {
        public string BurstId { get; set; }
        public string ContractId { get; set; }
        public int StoreId { get; set; }
        public string EmailAddress { get; set; }
        public string StoreName { get; set; }
        public string StoreManagerName { get; set; }
        public string SecondaryEmailAddres { get; set; }

        public string MediaName { get; set; }

        public DateTime FirstWeek { get; set; }

        public string InstallationInstructions { get; set; }

        public int NotificationNumber { get; set; }

        public DateTime FirstNotificationDateOfBurst { get; set; }

        public DateTime SecondNotificationDateOfBurst { get; set; }

        public string BrandName { get; set; }
        public string ProjectName { get; set; }

        public string StoreNumber { get; set; }
        public string InstallationDays { get; set; }

        public string CategoryList { get; set; }
        public string Email { get; set; }

        public string ContractNumber { get; set; }

        public string demoOwner { get; set; }
    }

    public class BurstNotificationStore
    {
        public int StoreId;
        public DateTime FirstWeek;
        public string StoreName;
        public string StoreNumber;
        public string BrandName;
        public string ProjectName;
        public string InstallationDays;
    }

    public class BurstNotificationBrandWeek
    {
        public DateTime FirstWeek;
        public string BrandName;
    }

    public class EmailListWithAttachements
    {
        public string EmailAddress;
        public string attachementPath;
    }


    public class ContractIssueNotification
    {
        public string ContractNumber { get; set; }
        public bool Singed { get; set; }
        public string ChainName { get; set; }
        public string ProjectName { get; set; }
        public string BrandName { get; set; }
        public string ClientName { get; set; }
        public string MediaName { get; set; }
        public DateTime FirstWeek { get; set; }
        public string StorelistStatus { get; set; }
        public string DemonstrationDaysText { get; set; }
        public string Email { get; set; }
    }
}
