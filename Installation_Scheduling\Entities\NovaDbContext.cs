﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PhoenixAPI.Models;


namespace PhoenixAPI.Entities
{
    public class UserDbContext : IdentityDbContext<ApplicationUser>
    {
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
               //optionsBuilder.UseSqlServer("Data Source=10.20.32.3;Initial Catalog=PhoenixUsers;Persist Security Info=True;User ID=sa;Password=************");
                optionsBuilder.UseSqlServer(@"Data Source=192.168.0.13\TEST;Initial Catalog=PhoenixUsers;Persist Security Info=True;User ID=SSIS;Password=****************$fRc");

            }
        }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            //builder.Entity<AppUser>().Ignore(e => e.FullName);
        }

    }
}
