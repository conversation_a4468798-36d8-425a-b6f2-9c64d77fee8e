﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AddScheduleDatesUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "isCurrent",
                schema: "Ops",
                table: "InstallationScheduleDates",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "isLatest",
                schema: "Ops",
                table: "InstallationScheduleDates",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "isCurrent",
                schema: "Ops",
                table: "InstallationScheduleDates");

            migrationBuilder.DropColumn(
                name: "isLatest",
                schema: "Ops",
                table: "InstallationScheduleDates");
        }
    }
}
