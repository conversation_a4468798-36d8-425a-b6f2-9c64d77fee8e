﻿using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PhoenixAPI.Models;
using PhoenixAPI.Services.Interfaces;
using System;
using PhoenixAPI.Services.EmailModel;

namespace PhoenixAPI.Controllers.OperationsManagement
{
    [ApiController]
    [EnableCors("EnableCORS")]
    [Authorize(Roles = "Ad<PERSON>, <PERSON>OpsUser, Storeman, MyMobilityUser")]
    [Route("api/OpsManagement/[controller]")]
    public class StockTakeController : ControllerBase
    {
        private readonly IStockTakeService _stockTakeServiceService;
        public StockTakeController(IStockTakeService stockTakeServiceService)
        {
            _stockTakeServiceService = stockTakeServiceService;
        }

        [HttpPost]
        [Route("CreateMasterInventoryItem")]
        public async Task<object> CreateMasterInventoryItem(MasterItemInventoryItem item)
        {
            var user = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

            try
            {
                return await _stockTakeServiceService.CreateMasterInventoryItemAsync(item, user);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [HttpPut]
        [Route("UpdateMasterInventoryItem")]
        public async Task<object> UpdateMasterInventoryItem(string barcode, int masterItemId)
        {
            try
            {
                return await _stockTakeServiceService.UpdateMasterItemAsync(barcode, masterItemId);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        //[HttpPut]
        //[Route("UpdateMasterItemLocation")]
        //public IActionResult UpdateMasterItemLocation(MasterItemLocation model)
        //{
        //    try
        //    {
        //        return Ok(_stockTakeServiceService.UpdateMasterItemLocationAsync(model));
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, ex.InnerException.Message);
        //    }
        //}

        [HttpPut]
        [Route("UpdateMasterItemOnStockTakeDetailsBarcodes")]
        public async Task<object> UpdateMasterItemOnStockTakeDetailsBarcodes(string barcode, int masterItemId, int inventoryStockTakeDetailId)
        {
            try
            {
                return await _stockTakeServiceService.UpdateMasterItemOnStockTakeDetailsBarcodesAsync(barcode, masterItemId, inventoryStockTakeDetailId);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("StockTakeInProgress")]
        [HttpGet]
        public async Task<object> StockTakeInProgress()
        {
            try
            {
                return await _stockTakeServiceService.StockTakeInProgress().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }

        [Route("CompleteStockTake")]
        [HttpPut]
        public async Task<object> CompleteStockTake(int stockTakeId)
        {
            try
            {
                return await _stockTakeServiceService.CompleteStockTake(stockTakeId).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.InnerException.Message);
            }
        }
    }
}
