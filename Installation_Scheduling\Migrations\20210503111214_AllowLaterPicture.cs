﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace PhoenixAPI.Migrations
{
    public partial class AllowLaterPicture : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "AllowPictureAfterwards",
                schema: "Ops",
                table: "InstallationScheduleCurrent",
                type: "bit",
                nullable: false,
                defaultValueSql: "((0))");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AllowPictureAfterwards",
                schema: "Ops",
                table: "InstallationScheduleCurrent");
        }
    }
}
