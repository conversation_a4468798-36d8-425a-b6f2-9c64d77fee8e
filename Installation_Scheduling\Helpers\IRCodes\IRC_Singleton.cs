﻿using PhoenixAPI.Models;
using System.Collections.Generic;

namespace PhoenixAPI.Helpers.IRCodesHelper
{
    public sealed class IRC
    {
        // The Singleton's constructor should always be private to prevent
        // direct construction calls with the `new` operator.
        private IRC() { }

        // The Singleton's instance is stored in a static field. There there are
        // multiple ways to initialize this field, all of them have various pros
        // and cons. In this example we'll show the simplest of these ways,
        // which, however, doesn't work really well in multithreaded program.
        private static IRC _instance;

        // This is the static method that controls the access to the singleton
        // instance. On the first run, it creates a singleton object and places
        // it into the static field. On subsequent runs, it returns the client
        // existing object stored in the static field.
        public static IRC GetInstance()
        {
            if (_instance == null)
            {
                _instance = new IRC();
            }
            return _instance;
        }

        private List<IRCodes> _list = new List<IRCodes>();

        // Finally, any singleton should define some business logic, which can
        // be executed on its instance.
        public void setList(List<IRCodes> list)
        {
            _list = list;
        }

        public List<IRCodes> getList()
        {
            return _list;
        }
    }
}
